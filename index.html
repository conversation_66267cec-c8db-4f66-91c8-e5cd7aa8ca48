<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>D-ID Avatar Chat</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="app-container">
        <header class="app-header">
            <h1>D-ID Avatar Chat</h1>
            <div class="connection-status">
                <span id="connection-status">Disconnected</span>
            </div>
        </header>

        <main class="main-content">
            <div class="avatar-section">
                <div class="avatar-container">
                    <video 
                        id="avatar-video" 
                        autoplay 
                        muted 
                        playsinline
                        poster="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='300' viewBox='0 0 400 300'%3E%3Crect width='400' height='300' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' dominant-baseline='middle' text-anchor='middle' font-family='Arial, sans-serif' font-size='18' fill='%23666'%3EAvatar will appear here%3C/text%3E%3C/svg%3E">
                    </video>
                    <div class="avatar-overlay" id="avatar-overlay">
                        <div class="loading-spinner" id="loading-spinner">
                            <div class="spinner"></div>
                            <p>Connecting to avatar...</p>
                        </div>
                    </div>
                </div>
                
                <div class="avatar-controls">
                    <button id="connect-btn" class="btn btn-primary">Connect</button>
                    <button id="disconnect-btn" class="btn btn-secondary" disabled>Disconnect</button>
                </div>
            </div>

            <div class="chat-section">
                <div class="chat-container">
                    <div class="chat-messages" id="chat-messages">
                        <div class="message system-message">
                            <p>Welcome! Connect to the avatar and start chatting.</p>
                        </div>
                    </div>
                    
                    <div class="chat-input-container">
                        <div class="input-group">
                            <input 
                                type="text" 
                                id="chat-input" 
                                placeholder="Type your message here..." 
                                disabled
                                maxlength="500"
                            >
                            <button id="send-btn" class="btn btn-send" disabled>
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <line x1="22" y1="2" x2="11" y2="13"></line>
                                    <polygon points="22,2 15,22 11,13 2,9"></polygon>
                                </svg>
                            </button>
                        </div>
                        <div class="input-controls">
                            <button id="voice-btn" class="btn btn-voice" disabled title="Voice input">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
                                    <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                                    <line x1="12" y1="19" x2="12" y2="23"></line>
                                    <line x1="8" y1="23" x2="16" y2="23"></line>
                                </svg>
                            </button>
                            <span class="char-counter">
                                <span id="char-count">0</span>/500
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <div class="config-panel" id="config-panel">
            <h3>Configuration</h3>
            <div class="config-item">
                <label for="agent-id">Agent ID:</label>
                <input type="text" id="agent-id" placeholder="Enter your agent ID" value="v2_agt_gLL2BIsAW">
            </div>
            <div class="config-item">
                <label for="client-key">Client Key:</label>
                <input type="text" id="client-key" placeholder="Enter your client key">
            </div>
            <button id="save-config" class="btn btn-primary">Save Configuration</button>
        </div>

        <button id="config-toggle" class="config-toggle" title="Configuration">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="3"></circle>
                <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"></path>
            </svg>
        </button>
    </div>

    <script type="module" src="main.js"></script>
</body>
</html>
