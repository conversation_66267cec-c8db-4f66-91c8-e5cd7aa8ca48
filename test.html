<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Avatar Mouth Animation</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: Arial, sans-serif;
            overflow: hidden;
        }
        
        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #controls {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 14px;
        }
        
        #micButton {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-bottom: 10px;
        }
        
        #micButton:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        #micButton.active {
            background: #f44336;
        }
        
        #volumeBar {
            width: 200px;
            height: 10px;
            background: #333;
            border-radius: 5px;
            overflow: hidden;
            margin-top: 5px;
        }
        
        #volumeLevel {
            height: 100%;
            background: #4CAF50;
            width: 0%;
            transition: width 0.1s;
        }
        
        #status {
            margin-top: 10px;
            font-size: 12px;
            color: #ccc;
        }
        
        #debug {
            margin-top: 10px;
            font-size: 11px;
            color: #aaa;
            max-width: 300px;
        }
        
        #loadingScreen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 18px;
            z-index: 200;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="loadingScreen">Loading avatar...</div>
        
        <div id="controls">
            <button id="micButton">Start Microphone</button>
            <div>Volume Level:</div>
            <div id="volumeBar">
                <div id="volumeLevel"></div>
            </div>
            <div id="status">Click to enable microphone</div>
            <div id="debug"></div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/GLTFLoader.js"></script>
    <script>
        // Three.js OrbitControls implementation (since it's not available via CDN for r128)
        class OrbitControls {
            constructor(object, domElement) {
                this.object = object;
                this.domElement = domElement;
                
                // Settings
                this.enabled = true;
                this.enableRotate = true;
                this.enableZoom = true;
                this.enablePan = true;
                
                this.rotateSpeed = 1.0;
                this.zoomSpeed = 1.0;
                this.panSpeed = 1.0;
                
                this.minDistance = 0;
                this.maxDistance = Infinity;
                
                // Internal state
                this.target = new THREE.Vector3();
                this.spherical = new THREE.Spherical();
                this.sphericalDelta = new THREE.Spherical();
                this.scale = 1;
                this.panOffset = new THREE.Vector3();
                
                this.rotateStart = new THREE.Vector2();
                this.rotateEnd = new THREE.Vector2();
                this.rotateDelta = new THREE.Vector2();
                
                this.panStart = new THREE.Vector2();
                this.panEnd = new THREE.Vector2();
                this.panDelta = new THREE.Vector2();
                
                this.zoomStart = new THREE.Vector2();
                this.zoomEnd = new THREE.Vector2();
                this.zoomDelta = new THREE.Vector2();
                
                this.STATE = { NONE: -1, ROTATE: 0, ZOOM: 1, PAN: 2 };
                this.state = this.STATE.NONE;
                
                this.bindEvents();
            }
            
            bindEvents() {
                this.domElement.addEventListener('mousedown', this.onMouseDown.bind(this));
                this.domElement.addEventListener('wheel', this.onMouseWheel.bind(this));
                this.domElement.addEventListener('contextmenu', (e) => e.preventDefault());
            }
            
            onMouseDown(event) {
                if (!this.enabled) return;
                
                event.preventDefault();
                
                if (event.button === 0) {
                    this.state = this.STATE.ROTATE;
                    this.rotateStart.set(event.clientX, event.clientY);
                } else if (event.button === 2) {
                    this.state = this.STATE.PAN;
                    this.panStart.set(event.clientX, event.clientY);
                }
                
                document.addEventListener('mousemove', this.onMouseMove.bind(this));
                document.addEventListener('mouseup', this.onMouseUp.bind(this));
            }
            
            onMouseMove(event) {
                if (!this.enabled) return;
                
                if (this.state === this.STATE.ROTATE) {
                    this.rotateEnd.set(event.clientX, event.clientY);
                    this.rotateDelta.subVectors(this.rotateEnd, this.rotateStart);
                    
                    const element = this.domElement;
                    
                    this.sphericalDelta.theta -= 2 * Math.PI * this.rotateDelta.x / element.clientHeight * this.rotateSpeed;
                    this.sphericalDelta.phi -= 2 * Math.PI * this.rotateDelta.y / element.clientHeight * this.rotateSpeed;
                    
                    this.rotateStart.copy(this.rotateEnd);
                }
            }
            
            onMouseUp() {
                document.removeEventListener('mousemove', this.onMouseMove.bind(this));
                document.removeEventListener('mouseup', this.onMouseUp.bind(this));
                this.state = this.STATE.NONE;
            }
            
            onMouseWheel(event) {
                if (!this.enabled || !this.enableZoom) return;
                
                event.preventDefault();
                
                if (event.deltaY < 0) {
                    this.scale *= 0.95;
                } else {
                    this.scale *= 1.05;
                }
            }
            
            update() {
                const offset = new THREE.Vector3();
                const quat = new THREE.Quaternion().setFromUnitVectors(this.object.up, new THREE.Vector3(0, 1, 0));
                const quatInverse = quat.clone().invert();
                
                const position = this.object.position;
                
                offset.copy(position).sub(this.target);
                offset.applyQuaternion(quat);
                
                this.spherical.setFromVector3(offset);
                
                this.spherical.theta += this.sphericalDelta.theta;
                this.spherical.phi += this.sphericalDelta.phi;
                
                this.spherical.radius *= this.scale;
                this.spherical.radius = Math.max(this.minDistance, Math.min(this.maxDistance, this.spherical.radius));
                
                this.target.add(this.panOffset);
                
                offset.setFromSpherical(this.spherical);
                offset.applyQuaternion(quatInverse);
                
                position.copy(this.target).add(offset);
                this.object.lookAt(this.target);
                
                this.sphericalDelta.set(0, 0, 0);
                this.scale = 1;
                this.panOffset.set(0, 0, 0);
            }
        }

        // Main application
        let scene, camera, renderer, controls;
        let avatar = null;
        let morphTargets = {};
        let jawBone = null;
        let audioContext = null;
        let microphone = null;
        let analyser = null;
        let dataArray = null;
        let isListening = false;
        
        // UI elements
        const micButton = document.getElementById('micButton');
        const volumeLevel = document.getElementById('volumeLevel');
        const status = document.getElementById('status');
        const debug = document.getElementById('debug');
        const loadingScreen = document.getElementById('loadingScreen');
        
        // Initialize Three.js scene
        function initScene() {
            // Scene
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x2a2a2a);
            
            // Camera
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(0, 1.6, 3);
            
            // Renderer
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            document.getElementById('container').appendChild(renderer.domElement);
            
            // Controls
            controls = new OrbitControls(camera, renderer.domElement);
            controls.target.set(0, 1.6, 0);
            
            // Lighting
            const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
            scene.add(ambientLight);
            
            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(5, 10, 5);
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);
            
            // Load avatar
            loadAvatar();
        }
        
        // Load GLB avatar
        function loadAvatar() {
            const loader = new THREE.GLTFLoader();
            
            loader.load(
                'model.glb',
                function(gltf) {
                    // Success callback
                    console.log('GLB model loaded successfully');
                    
                    const model = gltf.scene;
                    
                    // Center and scale the model
                    const box = new THREE.Box3().setFromObject(model);
                    const center = box.getCenter(new THREE.Vector3());
                    const size = box.getSize(new THREE.Vector3());
                    
                    // Scale model to reasonable size (assuming human-like proportions)
                    const maxDim = Math.max(size.x, size.y, size.z);
                    const scale = 2 / maxDim; // Scale to about 2 units tall
                    model.scale.setScalar(scale);
                    
                    // Center the model
                    model.position.sub(center.multiplyScalar(scale));
                    model.position.y = 0; // Place on ground
                    
                    scene.add(model);
                    avatar = model;
                    
                    // Look for morph targets and bones
                    findMouthControls(model);
                    
                    // Hide loading screen
                    loadingScreen.style.display = 'none';
                    status.textContent = 'Avatar loaded successfully! Click to enable microphone.';
                },
                function(progress) {
                    // Progress callback
                    const percent = (progress.loaded / progress.total * 100);
                    status.textContent = `Loading avatar... ${percent.toFixed(0)}%`;
                },
                function(error) {
                    // Error callback
                    console.warn('Could not load GLB file:', error);
                    createPlaceholderAvatar();
                }
            );
        }
        
        // Find mouth controls in the loaded model
        function findMouthControls(model) {
            let foundMouthControl = false;
            let debugInfo = [];
            
            // Look for morph targets (blendshapes)
            model.traverse(function(child) {
                if (child.isMesh && child.morphTargetDictionary) {
                    const morphNames = Object.keys(child.morphTargetDictionary);
                    console.log('Found mesh with morph targets:', child.name, morphNames);
                    debugInfo.push(`Mesh: ${child.name}, Morphs: ${morphNames.join(', ')}`);
                    
                    // Look for mouth-related morph targets (more comprehensive list)
                    const mouthTargets = [
                        'mouthOpen', 'mouth_open', 'MouthOpen', 'Mouth_Open', 
                        'jaw_open', 'jawOpen', 'JawOpen', 'Jaw_Open',
                        'viseme_aa', 'viseme_A', 'A', 'aa',
                        'mouthSmile', 'mouth_smile', 'smile',
                        'mouthFrown', 'mouth_frown', 'frown'
                    ];
                    
                    for (const targetName of mouthTargets) {
                        if (child.morphTargetDictionary[targetName] !== undefined) {
                            const index = child.morphTargetDictionary[targetName];
                            morphTargets.mouthOpen = {
                                mesh: child,
                                index: index,
                                name: targetName,
                                set influence(value) {
                                    if (child.morphTargetInfluences) {
                                        child.morphTargetInfluences[index] = Math.max(0, Math.min(1, value));
                                    }
                                },
                                get influence() {
                                    return child.morphTargetInfluences ? child.morphTargetInfluences[index] : 0;
                                }
                            };
                            console.log(`Found mouth morph target: ${targetName} at index ${index}`);
                            debugInfo.push(`Using morph: ${targetName}`);
                            foundMouthControl = true;
                            break;
                        }
                    }
                }
            });
            
            // If no morph targets found, look for jaw bone
            if (!foundMouthControl) {
                model.traverse(function(child) {
                    if (child.isBone || child.type === 'Bone') {
                        const name = child.name.toLowerCase();
                        debugInfo.push(`Bone found: ${child.name}`);
                        if (name.includes('jaw') || name.includes('chin') || name.includes('mouth') || 
                            name.includes('mandible') || name.includes('lower')) {
                            jawBone = child;
                            console.log(`Found jaw bone: ${child.name}`);
                            debugInfo.push(`Using jaw bone: ${child.name}`);
                            foundMouthControl = true;
                            return;
                        }
                    }
                });
            }
            
            // If still no control found, try to find any mesh that could be a mouth
            if (!foundMouthControl) {
                model.traverse(function(child) {
                    if (child.isMesh) {
                        const name = child.name.toLowerCase();
                        debugInfo.push(`Mesh found: ${child.name}`);
                        if (name.includes('mouth') || name.includes('teeth') || name.includes('tongue') ||
                            name.includes('lips') || name.includes('jaw')) {
                            jawBone = child;
                            console.log(`Found mouth mesh: ${child.name}`);
                            debugInfo.push(`Using mouth mesh: ${child.name}`);
                            foundMouthControl = true;
                            return;
                        }
                    }
                });
            }
            
            // If still nothing, just pick the first bone or interesting mesh
            if (!foundMouthControl) {
                model.traverse(function(child) {
                    if (!jawBone && (child.isBone || child.type === 'Bone')) {
                        jawBone = child;
                        debugInfo.push(`Fallback: Using first bone: ${child.name}`);
                        foundMouthControl = true;
                        return;
                    }
                });
            }
            
            // Update debug display
            debug.innerHTML = debugInfo.join('<br>');
            
            if (!foundMouthControl) {
                console.warn('No mouth controls found in the model. Animation may not work as expected.');
                status.textContent += ' (No mouth controls detected)';
                debug.innerHTML += '<br>No mouth controls found!';
            } else {
                console.log('Mouth control setup complete');
            }
        }
        
        function createPlaceholderAvatar() {
            // Create a simple avatar representation
            const avatarGroup = new THREE.Group();
            
            // Head
            const headGeometry = new THREE.SphereGeometry(0.5, 32, 32);
            const headMaterial = new THREE.MeshLambertMaterial({ color: 0xfdbcb4 });
            const head = new THREE.Mesh(headGeometry, headMaterial);
            head.position.set(0, 1.6, 0);
            avatarGroup.add(head);
            
            // Body
            const bodyGeometry = new THREE.CylinderGeometry(0.3, 0.4, 1.2, 12);
            const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x4a90e2 });
            const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
            body.position.set(0, 0.8, 0);
            avatarGroup.add(body);
            
            // Mouth area (this will be our animated element)
            const mouthGeometry = new THREE.SphereGeometry(0.15, 16, 8, 0, Math.PI * 2, 0, Math.PI);
            const mouthMaterial = new THREE.MeshLambertMaterial({ color: 0x8b4513 });
            const mouth = new THREE.Mesh(mouthGeometry, mouthMaterial);
            mouth.position.set(0, 1.4, 0.4);
            mouth.name = 'mouth';
            avatarGroup.add(mouth);
            
            // Store reference for animation
            jawBone = mouth;
            
            scene.add(avatarGroup);
            avatar = avatarGroup;
            
            // Hide loading screen
            loadingScreen.style.display = 'none';
            status.textContent = 'Placeholder avatar loaded. Click to enable microphone.';
        }
        
        // Initialize audio system
        async function initAudio() {
            try {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                microphone = audioContext.createMediaStreamSource(stream);
                
                analyser = audioContext.createAnalyser();
                analyser.fftSize = 256;
                analyser.smoothingTimeConstant = 0.8;
                
                microphone.connect(analyser);
                
                const bufferLength = analyser.frequencyBinCount;
                dataArray = new Uint8Array(bufferLength);
                
                isListening = true;
                micButton.textContent = 'Stop Microphone';
                micButton.classList.add('active');
                status.textContent = 'Microphone active - speak to animate mouth!';
                
                // Start audio analysis loop
                analyzeAudio();
                
            } catch (error) {
                console.error('Error accessing microphone:', error);
                status.textContent = 'Error: Could not access microphone';
            }
        }
        
        // Stop audio
        function stopAudio() {
            if (microphone && audioContext) {
                microphone.disconnect();
                audioContext.close();
                audioContext = null;
                microphone = null;
                isListening = false;
                
                micButton.textContent = 'Start Microphone';
                micButton.classList.remove('active');
                status.textContent = 'Microphone stopped';
                
                // Reset mouth position
                if (avatar) {
                    resetMouth();
                }
            }
        }
        
        // Analyze audio and update mouth animation
        function analyzeAudio() {
            if (!isListening || !analyser) return;
            
            analyser.getByteFrequencyData(dataArray);
            
            // Calculate volume level with focus on speech frequencies (300-3000 Hz)
            let volume = 0;
            const speechStart = Math.floor(300 * dataArray.length / (audioContext.sampleRate / 2));
            const speechEnd = Math.floor(3000 * dataArray.length / (audioContext.sampleRate / 2));
            
            for (let i = speechStart; i < Math.min(speechEnd, dataArray.length); i++) {
                volume += dataArray[i];
            }
            volume = volume / (speechEnd - speechStart) / 255; // Normalize to 0-1
            
            // Apply exponential scaling for better response
            volume = Math.pow(volume, 0.3); // Make it more sensitive
            volume = Math.min(1, volume * 3); // Amplify and clamp
            
            // Update UI
            volumeLevel.style.width = (volume * 100) + '%';
            
            // Show current volume in debug
            if (volume > 0.01) {
                debug.innerHTML = debug.innerHTML.split('<br>Volume:')[0] + '<br>Volume: ' + (volume * 100).toFixed(1) + '%';
            }
            
            // Animate mouth
            animateMouth(volume);
            
            // Continue analysis
            requestAnimationFrame(analyzeAudio);
        }
        
        // Animate mouth based on volume
        function animateMouth(volume) {
            if (!avatar) return;
            
            // Apply lower threshold and boost sensitivity
            const threshold = 0.01;
            const adjustedVolume = Math.max(0, volume - threshold) / (1 - threshold);
            const boostedVolume = Math.min(1, adjustedVolume * 1.5);
            
            // Check for morph targets first (blendshapes)
            if (morphTargets.mouthOpen) {
                try {
                    const targetValue = boostedVolume;
                    morphTargets.mouthOpen.influence = targetValue;
                    
                    // Debug output
                    if (targetValue > 0.1) {
                        console.log(`Setting morph ${morphTargets.mouthOpen.name} to ${targetValue.toFixed(2)}`);
                    }
                } catch (error) {
                    console.error('Error setting morph target:', error);
                }
            } else if (jawBone) {
                try {
                    // Fallback: animate jaw bone or mouth geometry
                    if (jawBone.isBone || jawBone.type === 'Bone') {
                        // Rotate jaw bone
                        const rotation = boostedVolume * 0.5; // Increased rotation amount
                        jawBone.rotation.x = rotation;
                        
                        if (rotation > 0.1) {
                            console.log(`Rotating jaw bone ${jawBone.name} to ${rotation.toFixed(2)} radians`);
                        }
                    } else if (jawBone.isMesh) {
                        // Scale or transform mesh
                        const scaleAmount = 1 + (boostedVolume * 0.5); // Increased scale amount
                        jawBone.scale.set(scaleAmount, scaleAmount * 0.7, scaleAmount);
                        
                        if (boostedVolume > 0.1) {
                            console.log(`Scaling mouth mesh ${jawBone.name} to ${scaleAmount.toFixed(2)}`);
                        }
                    }
                } catch (error) {
                    console.error('Error animating jaw bone/mesh:', error);
                }
            } else {
                // No controls found, try to animate the entire avatar as a test
                if (boostedVolume > 0.1) {
                    avatar.rotation.y = Math.sin(Date.now() * 0.01) * boostedVolume * 0.1;
                    console.log('No mouth controls - animating entire avatar as test');
                }
            }
        }
        
        // Reset mouth to default position
        function resetMouth() {
            if (morphTargets.mouthOpen) {
                morphTargets.mouthOpen.influence = 0;
            } else if (jawBone) {
                if (jawBone.isBone || jawBone.type === 'Bone') {
                    jawBone.rotation.x = 0;
                } else if (jawBone.isMesh) {
                    jawBone.scale.set(1, 1, 1);
                }
            } else if (avatar) {
                // Reset any test animations
                avatar.rotation.y = 0;
            }
        }
        
        // Animation loop
        function animate() {
            requestAnimationFrame(animate);
            
            if (controls) {
                controls.update();
            }
            
            renderer.render(scene, camera);
        }
        
        // Handle window resize
        function handleResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }
        
        // Event listeners
        micButton.addEventListener('click', () => {
            if (!isListening) {
                initAudio();
            } else {
                stopAudio();
            }
        });
        
        window.addEventListener('resize', handleResize);
        
        // Initialize application
        initScene();
        animate();
    </script>
</body>
</html>