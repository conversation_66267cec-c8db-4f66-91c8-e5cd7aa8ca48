import { StreamingManager } from '../streaming-manager';
import { Agent, AgentManagerOptions, AgentsAPI, Chat, CreateStreamOptions } from '../../types';
import { Analytics } from '../analytics/mixpanel';
type ConnectToManagerOptions = AgentManagerOptions & {
    callbacks: AgentManagerOptions['callbacks'] & {
        onVideoIdChange?: (videoId: string | null) => void;
    };
};
export declare function initializeStreamAndChat(agent: Agent, options: ConnectToManagerOptions, agentsApi: AgentsAPI, analytics: Analytics, chat?: Chat): Promise<{
    chat?: Chat;
    streamingManager?: StreamingManager<CreateStreamOptions>;
}>;
export {};
