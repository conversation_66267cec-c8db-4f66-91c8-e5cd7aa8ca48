# D-ID Avatar Chat Application

A real-time chat application with D-ID avatars that respond to your messages with video and audio.

## Features

- 🎭 Real-time D-ID avatar streaming
- 💬 Interactive chat interface
- 🎤 Voice input support (Web Speech API)
- 📱 Responsive design
- ⚙️ Easy configuration panel
- 🔄 Connection status monitoring

## Setup Instructions

### 1. Get Your D-ID Credentials

1. Go to [D-ID Studio](https://studio.d-id.com/)
2. Create or select an existing agent
3. Click on the "..." menu and select "Embed"
4. Set allowed domains (add `localhost` and `127.0.0.1`)
5. Copy the `data-agent-id` and `data-client-key` from the embed code

### 2. Install Dependencies

```bash
npm install
```

### 3. Configure the Application

1. Start the development server:
   ```bash
   npm run dev
   ```

2. Open your browser and go to `http://localhost:3000`

3. Click the settings icon (⚙️) in the top-right corner

4. Enter your:
   - **Agent ID**: The `data-agent-id` from D-ID Studio
   - **Client Key**: The `data-client-key` from D-ID Studio

5. Click "Save Configuration"

### 4. Start Chatting

1. Click the "Connect" button to establish connection with the avatar
2. Wait for the connection status to show "Connected"
3. Type your message in the chat input and press Enter or click Send
4. The avatar will respond with both video and audio

## Usage Tips

- **Voice Input**: Click the microphone button to use voice input (requires browser permission)
- **Character Limit**: Messages are limited to 500 characters
- **Connection Status**: Monitor the connection status in the header
- **Responsive**: Works on both desktop and mobile devices

## API Key Format

Your API key should be in the format: `username:password`

Example: `<EMAIL>:-v21mVTNfZBsEcYS5PvXV`

## Troubleshooting

### Connection Issues
- Ensure your client key is valid and not expired
- Check that your domain is added to the allowed domains list in D-ID Studio
- Verify your internet connection

### Avatar Not Appearing
- Check browser console for errors
- Ensure WebRTC is supported in your browser
- Try refreshing the page and reconnecting

### Voice Input Not Working
- Grant microphone permissions when prompted
- Ensure you're using a supported browser (Chrome, Edge, Safari)
- Check that your microphone is working

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build

### Project Structure

```
├── index.html          # Main HTML file
├── style.css           # Styles and responsive design
├── main.js             # Application logic and D-ID integration
├── package.json        # Dependencies and scripts
└── README.md          # This file
```

## Security Notes

- Never commit your API keys to version control
- Client keys are stored in localStorage for convenience
- Use environment variables for production deployments
- Regularly rotate your API keys

## Support

For D-ID API support, visit: https://docs.d-id.com/

For application issues, check the browser console for error messages.
