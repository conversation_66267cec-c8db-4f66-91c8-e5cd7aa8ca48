import { <PERSON>, <PERSON>Manager, AgentManagerOptions, Auth, Chat, ChatMode, CreateStreamOptions, Message } from '../../types';
import { SocketManager } from '../socket-manager';
import { StreamingManager } from '../streaming-manager';
export interface AgentManagerItems {
    chat?: Chat;
    streamingManager?: StreamingManager<CreateStreamOptions>;
    socketManager?: SocketManager;
    messages: Message[];
    chatMode: ChatMode;
}
/**
 * Creates a new Agent Manager instance for interacting with an agent, chat, and related connections.
 *
 * @param {string} agent - The ID or instance of the agent to chat with.
 * @param {AgentManagerOptions} options - Configurations for the Agent Manager API.
 * * @returns {Promise<AgentManager>} - A promise that resolves to an instance of the AgentsAPI interface.
 *
 * @throws {Error} Throws an error if the agent is not initialized.
 *
 * @example
 * const agentManager = await createAgentManager('id-agent123', { auth: { type: 'key', clientKey: '123', externalId: '123' } });
 */
export declare function createAgentManager(agent: string, options: AgentManagerOptions): Promise<AgentManager>;
export declare function getAgent(agentId: string, auth: Auth, baseURL?: string): Promise<Agent>;
