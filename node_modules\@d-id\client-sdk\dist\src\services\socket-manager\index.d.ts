import { ChatProgressCallback } from '../../types';
import { Auth } from '../../types/auth';
export interface SocketManager {
    socket?: WebSocket;
    disconnect: () => void;
    subscribeToEvents: (data: any) => void;
}
export declare function createSocketManager(auth: Auth, host: string, callbacks: {
    onMessage: ChatProgressCallback;
    onError?: (error: Error) => void;
}): Promise<SocketManager>;
