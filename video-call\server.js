const express = require('express');
const WebSocket = require('ws');
const http = require('http');
const path = require('path');
const cors = require('cors');
require('dotenv').config();

const app = express();
const server = http.createServer(app);
const wss = new WebSocket.Server({ server });

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));

// Store active Gemini sessions
const activeSessions = new Map();

// Serve the main HTML file
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Start a new Gemini Live session
app.post('/start-session', async (req, res) => {
    try {
        const sessionId = generateSessionId();
        
        // Create WebSocket connection to Gemini Live API
        const geminiWs = new WebSocket('wss://generativelanguage.googleapis.com/ws/google.ai.generativelanguage.v1alpha.GenerativeService.BidiGenerateContent', {
            headers: {
                'Authorization': `Bearer ${process.env.GEMINI_API_KEY}`,
                'Content-Type': 'application/json'
            }
        });

        // Store the session
        activeSessions.set(sessionId, {
            geminiWs,
            clientWs: null,
            isConnected: false
        });

        // Handle Gemini WebSocket connection
        geminiWs.on('open', () => {
            console.log(`Gemini session ${sessionId} connected`);
            
            // Initialize the session with Gemini 2.0 Flash model
            const initMessage = {
                setup: {
                    model: "models/gemini-2.0-flash-exp",
                    generation_config: {
                        response_modalities: ["AUDIO", "TEXT"],
                        speech_config: {
                            voice_config: {
                                prebuilt_voice_config: {
                                    voice_name: "Aoede"
                                }
                            }
                        }
                    },
                    system_instruction: {
                        parts: [{
                            text: "You are a helpful AI assistant in a live voice conversation. Keep responses conversational and natural."
                        }]
                    }
                }
            };
            
            geminiWs.send(JSON.stringify(initMessage));
            activeSessions.get(sessionId).isConnected = true;
        });

        geminiWs.on('message', (data) => {
            const session = activeSessions.get(sessionId);
            if (session && session.clientWs) {
                // Forward Gemini's response to the client
                const response = JSON.parse(data.toString());
                session.clientWs.send(JSON.stringify({
                    type: 'gemini_response',
                    data: response
                }));
            }
        });

        geminiWs.on('error', (error) => {
            console.error(`Gemini session ${sessionId} error:`, error);
        });

        geminiWs.on('close', () => {
            console.log(`Gemini session ${sessionId} closed`);
            activeSessions.delete(sessionId);
        });

        res.json({ sessionId, status: 'session_created' });
    } catch (error) {
        console.error('Error starting session:', error);
        res.status(500).json({ error: 'Failed to start session' });
    }
});

// WebSocket connection handler for clients
wss.on('connection', (ws, req) => {
    console.log('Client connected');
    
    ws.on('message', async (message) => {
        try {
            const data = JSON.parse(message.toString());
            
            switch (data.type) {
                case 'join_session':
                    const session = activeSessions.get(data.sessionId);
                    if (session) {
                        session.clientWs = ws;
                        ws.sessionId = data.sessionId;
                        ws.send(JSON.stringify({
                            type: 'session_joined',
                            sessionId: data.sessionId
                        }));
                    } else {
                        ws.send(JSON.stringify({
                            type: 'error',
                            message: 'Session not found'
                        }));
                    }
                    break;
                    
                case 'audio_chunk':
                    // Forward audio chunk to Gemini
                    const audioSession = activeSessions.get(ws.sessionId);
                    if (audioSession && audioSession.isConnected) {
                        const geminiMessage = {
                            realtime_input: {
                                media_chunks: [{
                                    mime_type: "audio/pcm",
                                    data: data.audioData
                                }]
                            }
                        };
                        audioSession.geminiWs.send(JSON.stringify(geminiMessage));
                    }
                    break;
                    
                case 'text_input':
                    // Send text input to Gemini
                    const textSession = activeSessions.get(ws.sessionId);
                    if (textSession && textSession.isConnected) {
                        const geminiMessage = {
                            client_content: {
                                turns: [{
                                    role: "user",
                                    parts: [{
                                        text: data.text
                                    }]
                                }],
                                turn_complete: true
                            }
                        };
                        textSession.geminiWs.send(JSON.stringify(geminiMessage));
                    }
                    break;
            }
        } catch (error) {
            console.error('Error handling message:', error);
            ws.send(JSON.stringify({
                type: 'error',
                message: 'Failed to process message'
            }));
        }
    });
    
    ws.on('close', () => {
        console.log('Client disconnected');
        if (ws.sessionId) {
            const session = activeSessions.get(ws.sessionId);
            if (session) {
                session.clientWs = null;
            }
        }
    });
});

// Utility function to generate session IDs
function generateSessionId() {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
    console.log(`Server running on http://localhost:${PORT}`);
});
