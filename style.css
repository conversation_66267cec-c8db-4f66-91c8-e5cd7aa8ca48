/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.app-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.app-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 20px 30px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
    font-size: 2rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 8px;
}

#connection-status {
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    background: #ef4444;
    color: white;
    transition: all 0.3s ease;
}

#connection-status.connected {
    background: #10b981;
}

#connection-status.connecting {
    background: #f59e0b;
}

/* Main content */
.main-content {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    height: calc(100vh - 140px);
}

/* Avatar section */
.avatar-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.avatar-container {
    flex: 1;
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    background: #f8fafc;
    margin-bottom: 20px;
}

#avatar-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 12px;
}

.avatar-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    transition: opacity 0.3s ease;
}

.avatar-overlay.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.avatar-controls {
    display: flex;
    gap: 12px;
}

/* Chat section */
.chat-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    background: #f8fafc;
    border-radius: 12px;
    margin-bottom: 16px;
    max-height: calc(100vh - 300px);
}

.message {
    margin-bottom: 16px;
    padding: 12px 16px;
    border-radius: 12px;
    max-width: 80%;
    word-wrap: break-word;
}

.message.user {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    margin-left: auto;
    border-bottom-right-radius: 4px;
}

.message.assistant {
    background: white;
    border: 1px solid #e2e8f0;
    border-bottom-left-radius: 4px;
}

.message.system-message {
    background: #f1f5f9;
    color: #64748b;
    text-align: center;
    margin: 0 auto;
    font-style: italic;
}

.chat-input-container {
    background: white;
    border-radius: 12px;
    border: 2px solid #e2e8f0;
    transition: border-color 0.3s ease;
}

.chat-input-container:focus-within {
    border-color: #667eea;
}

.input-group {
    display: flex;
    align-items: center;
    padding: 4px;
}

#chat-input {
    flex: 1;
    border: none;
    outline: none;
    padding: 12px 16px;
    font-size: 1rem;
    border-radius: 8px;
    background: transparent;
}

#chat-input:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.input-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 16px;
    border-top: 1px solid #e2e8f0;
}

.char-counter {
    font-size: 0.8rem;
    color: #64748b;
}

/* Buttons */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #64748b;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #475569;
}

.btn-send {
    background: #10b981;
    color: white;
    padding: 12px;
    border-radius: 8px;
}

.btn-send:hover:not(:disabled) {
    background: #059669;
}

.btn-voice {
    background: #f59e0b;
    color: white;
    padding: 8px;
    border-radius: 6px;
}

.btn-voice:hover:not(:disabled) {
    background: #d97706;
}

.btn-voice.recording {
    background: #ef4444;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Configuration panel */
.config-panel {
    position: fixed;
    top: 20px;
    right: -320px;
    width: 300px;
    background: white;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    transition: right 0.3s ease;
    z-index: 1000;
}

.config-panel.open {
    right: 20px;
}

.config-panel h3 {
    margin-bottom: 20px;
    color: #1f2937;
}

.config-item {
    margin-bottom: 16px;
}

.config-item label {
    display: block;
    margin-bottom: 4px;
    font-weight: 600;
    color: #374151;
}

.config-item input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.9rem;
}

.config-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 1001;
}

.config-toggle:hover {
    transform: scale(1.1);
}

/* Responsive design */
@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .app-header {
        padding: 16px 20px;
    }
    
    .app-header h1 {
        font-size: 1.5rem;
    }
    
    .config-panel {
        width: calc(100vw - 40px);
        right: -100vw;
    }
    
    .config-panel.open {
        right: 20px;
    }
}
