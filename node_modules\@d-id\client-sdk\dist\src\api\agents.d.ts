import { Agent, AgentPayload, Auth, Chat, ChatPayload, ChatResponse, RatingEntity, RatingPayload, STTTokenResponse } from '../types/index';
import { RequestOptions } from './apiClient';
export declare function createAgentsApi(auth: Auth, host?: string, onError?: (error: Error, errorData: object) => void): {
    create(payload: AgentPayload, options?: RequestOptions): Promise<Agent>;
    getAgents(tag?: string, options?: RequestOptions): Promise<Agent[]>;
    getById(id: string, options?: RequestOptions): Promise<Agent>;
    delete(id: string, options?: RequestOptions): Promise<any>;
    update(id: string, payload: AgentPayload, options?: RequestOptions): Promise<Agent>;
    newChat(agentId: string, payload: {
        persist: boolean;
    }, options?: RequestOptions): Promise<Chat>;
    chat(agentId: string, chatId: string, payload: ChatPayload, options?: RequestOptions): Promise<ChatResponse>;
    createRating(agentId: string, chatId: string, payload: RatingPayload, options?: RequestOptions): Promise<RatingEntity>;
    updateRating(agentId: string, chatId: string, ratingId: string, payload: Partial<RatingPayload>, options?: RequestOptions): Promise<RatingEntity>;
    deleteRating(agentId: string, chatId: string, ratingId: string, options?: RequestOptions): Promise<RatingEntity>;
    getSTTToken(agentId: string, options?: RequestOptions): Promise<STTTokenResponse>;
};
