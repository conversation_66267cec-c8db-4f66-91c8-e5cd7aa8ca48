import { CreateStreamOptions, PayloadType, StreamType, StreamingManagerOptions } from '../../types/index';
export declare function createStreamingManager<T extends CreateStreamOptions>(agentId: string, agent: T, { debug, callbacks, auth, baseURL, analytics }: StreamingManagerOptions): Promise<{
    /**
     * Method to send request to server to get clip or talk depend on you payload
     * @param payload
     */
    speak(payload: PayloadType<T>): Promise<import('../../types/index').SendStreamPayloadResponse>;
    /**
     * Method to close RTC connection
     */
    disconnect(): Promise<void>;
    /**
     * Method to send data channel messages to the server
     */
    sendDataChannelMessage(payload: string): void;
    /**
     * Session identifier information, should be returned in the body of all streaming requests
     */
    sessionId: string;
    /**
     * Id of current RTC stream
     */
    streamId: string;
    streamType: StreamType;
    interruptAvailable: boolean | undefined;
}>;
export type StreamingManager<T extends CreateStreamOptions> = Awaited<ReturnType<typeof createStreamingManager<T>>>;
