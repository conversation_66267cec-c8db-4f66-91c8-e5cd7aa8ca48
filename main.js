import * as sdk from '@d-id/client-sdk';

// Configuration
let agentId = 'v2_agt_gLL2BIsAW'; // Default agent ID
let clientKey = 'DEMO_CLIENT_KEY_REPLACE_WITH_REAL_KEY'; // Demo key - replace with real client key from D-ID Studio

// Global variables
let agentManager = null;
let srcObject = null;
let isConnected = false;
let isRecording = false;
let recognition = null;

// DOM elements
const elements = {
    connectBtn: document.getElementById('connect-btn'),
    disconnectBtn: document.getElementById('disconnect-btn'),
    avatarVideo: document.getElementById('avatar-video'),
    avatarOverlay: document.getElementById('avatar-overlay'),
    loadingSpinner: document.getElementById('loading-spinner'),
    chatMessages: document.getElementById('chat-messages'),
    chatInput: document.getElementById('chat-input'),
    sendBtn: document.getElementById('send-btn'),
    voiceBtn: document.getElementById('voice-btn'),
    charCount: document.getElementById('char-count'),
    connectionStatus: document.getElementById('connection-status'),
    configPanel: document.getElementById('config-panel'),
    configToggle: document.getElementById('config-toggle'),
    agentIdInput: document.getElementById('agent-id'),
    clientKeyInput: document.getElementById('client-key'),
    saveConfigBtn: document.getElementById('save-config')
};

// D-ID SDK Configuration
const streamOptions = {
    compatibilityMode: 'auto',
    streamWarmup: true
};

const callbacks = {
    onSrcObjectReady(value) {
        console.log('onSrcObjectReady:', value);
        elements.avatarVideo.srcObject = value;
        srcObject = value;
        return srcObject;
    },

    onVideoStateChange(state) {
        console.log('onVideoStateChange:', state);
        if (state === 'STOP') {
            elements.avatarVideo.srcObject = undefined;
            if (agentManager?.agent?.presenter?.idle_video) {
                elements.avatarVideo.src = agentManager.agent.presenter.idle_video;
            }
        } else {
            elements.avatarVideo.src = '';
            elements.avatarVideo.srcObject = srcObject;
        }
    },

    onConnectionStateChange(state) {
        console.log('onConnectionStateChange:', state);
        updateConnectionStatus(state);
        
        if (state === 'connected') {
            isConnected = true;
            elements.avatarOverlay.classList.add('hidden');
            elements.connectBtn.disabled = true;
            elements.disconnectBtn.disabled = false;
            elements.chatInput.disabled = false;
            elements.sendBtn.disabled = false;
            elements.voiceBtn.disabled = false;
            
            addMessage('system', 'Connected! You can now start chatting with the avatar.');
        } else if (state === 'disconnected' || state === 'closed') {
            isConnected = false;
            elements.avatarOverlay.classList.remove('hidden');
            elements.connectBtn.disabled = false;
            elements.disconnectBtn.disabled = true;
            elements.chatInput.disabled = true;
            elements.sendBtn.disabled = true;
            elements.voiceBtn.disabled = true;
        }
    },

    onNewMessage(messages, type) {
        console.log('onNewMessage:', messages, type);
        if (type === 'answer' && messages.length > 0) {
            const lastMessage = messages[messages.length - 1];
            if (lastMessage.role === 'assistant') {
                addMessage('assistant', lastMessage.content);
            }
        }
    },

    onError(error, errorData) {
        console.error('D-ID Error:', error, errorData);
        addMessage('system', `Error: ${error}`);
        updateConnectionStatus('error');
    }
};

// Initialize the application
async function init() {
    setupEventListeners();
    setupSpeechRecognition();
    loadConfiguration();
    
    // Load idle video if available
    if (agentId) {
        try {
            // We'll set a placeholder for now since we don't have the client key
            elements.avatarVideo.poster = "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='300' viewBox='0 0 400 300'%3E%3Crect width='400' height='300' fill='%23f0f0f0'/%3E%3Ctext x='50%25' y='50%25' dominant-baseline='middle' text-anchor='middle' font-family='Arial, sans-serif' font-size='18' fill='%23666'%3EAvatar will appear here%3C/text%3E%3C/svg%3E";
        } catch (error) {
            console.error('Error loading avatar preview:', error);
        }
    }
}

// Setup event listeners
function setupEventListeners() {
    elements.connectBtn.addEventListener('click', connectToAgent);
    elements.disconnectBtn.addEventListener('click', disconnectFromAgent);
    elements.sendBtn.addEventListener('click', sendMessage);
    elements.voiceBtn.addEventListener('click', toggleVoiceRecording);
    elements.configToggle.addEventListener('click', toggleConfigPanel);
    elements.saveConfigBtn.addEventListener('click', saveConfiguration);
    
    elements.chatInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });
    
    elements.chatInput.addEventListener('input', updateCharCount);
}

// Setup speech recognition
function setupSpeechRecognition() {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        recognition = new SpeechRecognition();
        recognition.continuous = false;
        recognition.interimResults = false;
        recognition.lang = 'en-US';
        
        recognition.onresult = (event) => {
            const transcript = event.results[0][0].transcript;
            elements.chatInput.value = transcript;
            updateCharCount();
        };
        
        recognition.onend = () => {
            isRecording = false;
            elements.voiceBtn.classList.remove('recording');
        };
        
        recognition.onerror = (event) => {
            console.error('Speech recognition error:', event.error);
            isRecording = false;
            elements.voiceBtn.classList.remove('recording');
        };
    } else {
        elements.voiceBtn.style.display = 'none';
    }
}

// Connect to D-ID agent
async function connectToAgent() {
    if (!clientKey || clientKey === 'DEMO_CLIENT_KEY_REPLACE_WITH_REAL_KEY') {
        addMessage('system', 'Please configure your real client key in the settings panel. Get it from D-ID Studio.');
        toggleConfigPanel();
        return;
    }
    
    try {
        elements.connectBtn.disabled = true;
        elements.loadingSpinner.querySelector('p').textContent = 'Connecting to avatar...';
        updateConnectionStatus('connecting');
        
        const auth = { type: 'key', clientKey };
        agentManager = await sdk.createAgentManager(agentId, { auth, callbacks, streamOptions });
        
        await agentManager.connect();
        
    } catch (error) {
        console.error('Connection error:', error);
        addMessage('system', `Failed to connect: ${error.message}`);
        elements.connectBtn.disabled = false;
        updateConnectionStatus('error');
    }
}

// Disconnect from agent
async function disconnectFromAgent() {
    if (agentManager) {
        try {
            await agentManager.disconnect();
            agentManager = null;
        } catch (error) {
            console.error('Disconnect error:', error);
        }
    }
}

// Send message to agent
async function sendMessage() {
    const message = elements.chatInput.value.trim();
    if (!message || !isConnected || !agentManager) return;
    
    try {
        addMessage('user', message);
        elements.chatInput.value = '';
        updateCharCount();
        
        await agentManager.chat(message);
        
    } catch (error) {
        console.error('Send message error:', error);
        addMessage('system', `Failed to send message: ${error.message}`);
    }
}

// Toggle voice recording
function toggleVoiceRecording() {
    if (!recognition) return;
    
    if (isRecording) {
        recognition.stop();
    } else {
        recognition.start();
        isRecording = true;
        elements.voiceBtn.classList.add('recording');
    }
}

// Add message to chat
function addMessage(role, content) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${role}`;
    
    const messageContent = document.createElement('p');
    messageContent.textContent = content;
    messageDiv.appendChild(messageContent);
    
    if (role !== 'system') {
        const timestamp = document.createElement('small');
        timestamp.textContent = new Date().toLocaleTimeString();
        timestamp.style.opacity = '0.7';
        timestamp.style.fontSize = '0.8rem';
        messageDiv.appendChild(timestamp);
    }
    
    elements.chatMessages.appendChild(messageDiv);
    elements.chatMessages.scrollTop = elements.chatMessages.scrollHeight;
}

// Update connection status
function updateConnectionStatus(status) {
    const statusElement = elements.connectionStatus;
    statusElement.className = '';
    
    switch (status) {
        case 'connected':
            statusElement.textContent = 'Connected';
            statusElement.classList.add('connected');
            break;
        case 'connecting':
            statusElement.textContent = 'Connecting...';
            statusElement.classList.add('connecting');
            break;
        case 'disconnected':
        case 'closed':
            statusElement.textContent = 'Disconnected';
            break;
        case 'error':
            statusElement.textContent = 'Error';
            break;
        default:
            statusElement.textContent = 'Disconnected';
    }
}

// Update character count
function updateCharCount() {
    const count = elements.chatInput.value.length;
    elements.charCount.textContent = count;
    
    if (count > 450) {
        elements.charCount.style.color = '#ef4444';
    } else if (count > 400) {
        elements.charCount.style.color = '#f59e0b';
    } else {
        elements.charCount.style.color = '#64748b';
    }
}

// Toggle configuration panel
function toggleConfigPanel() {
    elements.configPanel.classList.toggle('open');
}

// Save configuration
function saveConfiguration() {
    agentId = elements.agentIdInput.value.trim();
    clientKey = elements.clientKeyInput.value.trim();
    
    if (!agentId || !clientKey) {
        alert('Please enter both Agent ID and Client Key');
        return;
    }
    
    // Save to localStorage
    localStorage.setItem('d-id-agent-id', agentId);
    localStorage.setItem('d-id-client-key', clientKey);
    
    addMessage('system', 'Configuration saved successfully!');
    toggleConfigPanel();
}

// Load configuration
function loadConfiguration() {
    const savedAgentId = localStorage.getItem('d-id-agent-id');
    const savedClientKey = localStorage.getItem('d-id-client-key');

    if (savedAgentId) {
        agentId = savedAgentId;
        elements.agentIdInput.value = savedAgentId;
    } else {
        elements.agentIdInput.value = agentId;
    }

    if (savedClientKey) {
        clientKey = savedClientKey;
        elements.clientKeyInput.value = savedClientKey;
    } else {
        elements.clientKeyInput.value = clientKey;
        elements.clientKeyInput.placeholder = 'Get your client key from D-ID Studio';
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', init);
