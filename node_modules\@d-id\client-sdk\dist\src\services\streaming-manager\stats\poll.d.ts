/// <reference types="node" />
import { ConnectivityState, StreamingState } from '../../../types';
import { VideoRTCStatsReport } from './report';
export declare function pollStats(peerConnection: RTCPeerConnection, getIsConnected: () => boolean, onConnected: () => void, onVideoStateChange?: (state: StreamingState, statsReport?: VideoRTCStatsReport) => void, onConnectivityStateChange?: (state: ConnectivityState) => void, warmup?: boolean): NodeJS.Timeout;
