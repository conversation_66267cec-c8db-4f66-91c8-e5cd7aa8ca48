import { Auth, CreateDocumentPayload, DocumentData, KnowledgeData, KnowledgePayload, QueryResult, RecordData } from '../types/index';
export declare function createKnowledgeApi(auth: Auth, host?: string, onError?: (error: Error, errorData: object) => void): {
    createKnowledge(payload: KnowledgePayload, options?: RequestInit): Promise<KnowledgeData>;
    getKnowledgeBase(options?: RequestInit): Promise<KnowledgeData[]>;
    getKnowledge(knowledgeId: string, options?: RequestInit): Promise<KnowledgeData>;
    deleteKnowledge(knowledgeId: string, options?: RequestInit): Promise<any>;
    createDocument(knowledgeId: string, payload: CreateDocumentPayload, options?: RequestInit): Promise<DocumentData>;
    deleteDocument(knowledgeId: string, documentId: string, options?: RequestInit): Promise<any>;
    getDocuments(knowledgeId: string, options?: RequestInit): Promise<DocumentData[]>;
    getDocument(knowledgeId: string, documentId: string, options?: RequestInit): Promise<DocumentData>;
    getRecords(knowledgeId: string, documentId: string, options?: RequestInit): Promise<RecordData[]>;
    query(knowledgeId: string, query: string, options?: RequestInit): Promise<QueryResult>;
};
export type KnowledegeApi = ReturnType<typeof createKnowledgeApi>;
