import { Agent } from '../types/index';
export declare function getAnalyticsInfo(agent: Agent): {
    $os: string;
    isMobile: string;
    browser: string;
    origin: string;
    agentType: "talk" | "clip" | "clip_v2";
    agentVoice: {
        voiceId: string | undefined;
        provider: import('../types/index').Providers | undefined;
    };
};
export declare function getAgentInfo(agent: Agent): {
    from?: string | undefined;
    agentType: "talk" | "clip" | "clip_v2";
    owner_id: string;
    promptVersion: "v1" | "v2" | null | undefined;
    behavior: {
        role: string | undefined;
        personality: string | undefined;
        instructions: string | undefined;
    };
    temperature: number | undefined;
    knowledgeSource: "base_knowledge" | "documents" | null | undefined;
    starterQuestionsCount: number | undefined;
    topicsToAvoid: string[] | undefined;
    maxResponseLength: number | undefined;
    agentId: string;
    access: "public" | "private" | "rejected" | "pending-public" | "unlisted" | undefined;
    name: string | undefined;
};
export declare const sumFunc: (numbers: number[]) => number;
export declare const average: (numbers: number[]) => number;
export declare function getStreamAnalyticsProps(data: any, agent: Agent, additionalProps: Record<string, any>): any;
