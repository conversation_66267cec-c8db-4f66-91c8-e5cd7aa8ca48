var ye = Object.defineProperty;
var ve = (e, t, n) => t in e ? ye(e, t, { enumerable: !0, configurable: !0, writable: !0, value: n }) : e[t] = n;
var Y = (e, t, n) => (ve(e, typeof t != "symbol" ? t + "" : t, n), n);
class G extends Error {
  constructor({
    kind: n,
    description: r,
    error: s
  }) {
    super(JSON.stringify({
      kind: n,
      description: r
    }));
    Y(this, "kind");
    Y(this, "description");
    Y(this, "error");
    this.kind = n, this.description = r, this.error = s;
  }
}
class ke extends G {
  constructor(t, n) {
    super({
      kind: "ChatCreationFailed",
      description: `Failed to create ${n ? "persistent" : ""} chat, mode: ${t}`
    });
  }
}
class De extends G {
  constructor(t) {
    super({
      kind: "ChatModeDowngraded",
      description: `Chat mode downgraded to ${t}`
    });
  }
}
class H extends G {
  constructor(n, r) {
    super({
      kind: "ValidationError",
      description: n
    });
    Y(this, "key");
    this.key = r;
  }
}
class Ce extends G {
  constructor(t) {
    super({
      kind: "WSError",
      description: t
    });
  }
}
var Re = /* @__PURE__ */ ((e) => (e.TRIAL = "trial", e.BASIC = "basic", e.ENTERPRISE = "enterprise", e.LITE = "lite", e.ADVANCED = "advanced", e))(Re || {}), Se = /* @__PURE__ */ ((e) => (e.TRIAL = "deid-trial", e.PRO = "deid-pro", e.ENTERPRISE = "deid-enterprise", e.LITE = "deid-lite", e.ADVANCED = "deid-advanced", e.BUILD = "deid-api-build", e.LAUNCH = "deid-api-launch", e.SCALE = "deid-api-scale", e))(Se || {}), Ie = /* @__PURE__ */ ((e) => (e.Created = "created", e.Started = "started", e.Done = "done", e.Error = "error", e.Rejected = "rejected", e.Ready = "ready", e))(Ie || {}), Ee = /* @__PURE__ */ ((e) => (e.Unrated = "Unrated", e.Positive = "Positive", e.Negative = "Negative", e))(Ee || {}), M = /* @__PURE__ */ ((e) => (e.Functional = "Functional", e.TextOnly = "TextOnly", e.Maintenance = "Maintenance", e.Playground = "Playground", e.DirectPlayback = "DirectPlayback", e.Off = "Off", e))(M || {}), q = /* @__PURE__ */ ((e) => (e.Embed = "embed", e.Query = "query", e.Partial = "partial", e.Answer = "answer", e.Complete = "done", e))(q || {}), Me = /* @__PURE__ */ ((e) => (e.KnowledgeProcessing = "knowledge/processing", e.KnowledgeIndexing = "knowledge/indexing", e.KnowledgeFailed = "knowledge/error", e.KnowledgeDone = "knowledge/done", e))(Me || {}), _e = /* @__PURE__ */ ((e) => (e.Knowledge = "knowledge", e.Document = "document", e.Record = "record", e))(_e || {}), Te = /* @__PURE__ */ ((e) => (e.Pdf = "pdf", e.Text = "text", e.Html = "html", e.Word = "word", e.Json = "json", e.Markdown = "markdown", e.Csv = "csv", e.Excel = "excel", e.Powerpoint = "powerpoint", e.Archive = "archive", e.Image = "image", e.Audio = "audio", e.Video = "video", e))(Te || {}), je = /* @__PURE__ */ ((e) => (e.Clip = "clip", e.Talk = "talk", e))(je || {});
const It = (e) => {
  switch (e) {
    case "clip":
      return "clip";
    case "talk":
      return "talk";
    default:
      throw new Error(`Unknown video type: ${e}`);
  }
};
var h = /* @__PURE__ */ ((e) => (e.Start = "START", e.Stop = "STOP", e))(h || {}), Q = /* @__PURE__ */ ((e) => (e.Strong = "STRONG", e.Weak = "WEAK", e.Unknown = "UNKNOWN", e))(Q || {}), O = /* @__PURE__ */ ((e) => (e.Idle = "IDLE", e.Talking = "TALKING", e))(O || {}), A = /* @__PURE__ */ ((e) => (e.ChatAnswer = "chat/answer", e.ChatPartial = "chat/partial", e.StreamDone = "stream/done", e.StreamStarted = "stream/started", e.StreamFailed = "stream/error", e.StreamReady = "stream/ready", e.StreamCreated = "stream/created", e.StreamInterrupt = "stream/interrupt", e.StreamVideoCreated = "stream-video/started", e.StreamVideoDone = "stream-video/done", e.StreamVideoError = "stream-video/error", e.StreamVideoRejected = "stream-video/rejected", e))(A || {}), I = /* @__PURE__ */ ((e) => (e.New = "new", e.Fail = "fail", e.Connected = "connected", e.Connecting = "connecting", e.Closed = "closed", e.Completed = "completed", e.Disconnected = "disconnected", e))(I || {}), W = /* @__PURE__ */ ((e) => (e.Legacy = "legacy", e.Fluent = "fluent", e))(W || {}), Pe = /* @__PURE__ */ ((e) => (e.Amazon = "amazon", e.Microsoft = "microsoft", e.Afflorithmics = "afflorithmics", e.Elevenlabs = "elevenlabs", e))(Pe || {}), be = /* @__PURE__ */ ((e) => (e.Public = "public", e.Premium = "premium", e.Private = "private", e))(be || {});
const Ae = 45 * 1e3, Be = "X-Playground-Chat", Z = "https://api.d-id.com", Le = "wss://notifications.d-id.com", $e = "79f81a83a67430be2bc0fd61042b8faa", oe = (e) => new Promise((t) => setTimeout(t, e)), V = (e = 16) => {
  const t = new Uint8Array(e);
  return window.crypto.getRandomValues(t), Array.from(t, (n) => n.toString(16).padStart(2, "0")).join("").slice(0, 13);
}, ze = (e) => [M.TextOnly, M.Playground, M.Maintenance].includes(e), ce = (e) => e && [M.DirectPlayback, M.Off].includes(e);
function Fe(e, t) {
  let n;
  return {
    promise: new Promise((s, o) => {
      n = setTimeout(() => o(new Error(t)), e);
    }),
    clear: () => clearTimeout(n)
  };
}
async function te(e, t) {
  const n = {
    limit: (t == null ? void 0 : t.limit) ?? 3,
    delayMs: (t == null ? void 0 : t.delayMs) ?? 0,
    timeout: (t == null ? void 0 : t.timeout) ?? 3e4,
    timeoutErrorMessage: (t == null ? void 0 : t.timeoutErrorMessage) || "Timeout error",
    shouldRetryFn: (t == null ? void 0 : t.shouldRetryFn) ?? (() => !0),
    onRetry: (t == null ? void 0 : t.onRetry) ?? (() => {
    })
  };
  let r;
  for (let s = 1; s <= n.limit; s++)
    try {
      if (!n.timeout)
        return await e();
      const {
        promise: o,
        clear: c
      } = Fe(n.timeout, n.timeoutErrorMessage), a = e().finally(c);
      return await Promise.race([a, o]);
    } catch (o) {
      if (r = o, !n.shouldRetryFn(o) || s >= n.limit)
        throw o;
      await oe(n.delayMs), n.onRetry(o);
    }
  throw r;
}
function de() {
  let e = window.localStorage.getItem("did_external_key_id");
  if (!e) {
    let t = V();
    window.localStorage.setItem("did_external_key_id", t), e = t;
  }
  return e;
}
let Ne = V();
function le(e) {
  if (e.type === "bearer")
    return `Bearer ${e.token}`;
  if (e.type === "basic")
    return `Basic ${btoa(`${e.username}:${e.password}`)}`;
  if (e.type === "key")
    return `Client-Key ${e.clientKey}.${de()}_${Ne}`;
  throw new Error(`Unknown auth type: ${e}`);
}
const xe = (e) => te(e, {
  limit: 3,
  delayMs: 1e3,
  timeout: 0,
  shouldRetryFn: (t) => t.status === 429
});
function ue(e, t = Z, n) {
  const r = async (s, o) => {
    const {
      skipErrorHandler: c,
      ...a
    } = o || {}, i = await xe(() => fetch(t + (s != null && s.startsWith("/") ? s : `/${s}`), {
      ...a,
      headers: {
        ...a.headers,
        Authorization: le(e),
        "Content-Type": "application/json"
      }
    }));
    if (!i.ok) {
      let d = await i.text().catch(() => `Failed to fetch with status ${i.status}`);
      const l = new Error(d);
      throw n && !c && n(l, {
        url: s,
        options: a,
        headers: i.headers
      }), l;
    }
    return i.json();
  };
  return {
    get(s, o) {
      return r(s, {
        ...o,
        method: "GET"
      });
    },
    post(s, o, c) {
      return r(s, {
        ...c,
        body: JSON.stringify(o),
        method: "POST"
      });
    },
    delete(s, o, c) {
      return r(s, {
        ...c,
        body: JSON.stringify(o),
        method: "DELETE"
      });
    },
    patch(s, o, c) {
      return r(s, {
        ...c,
        body: JSON.stringify(o),
        method: "PATCH"
      });
    }
  };
}
function fe(e, t = Z, n) {
  const r = ue(e, `${t}/agents`, n);
  return {
    create(s, o) {
      return r.post("/", s, o);
    },
    getAgents(s, o) {
      return r.get(`/${s ? `?tag=${s}` : ""}`, o).then((c) => c ?? []);
    },
    getById(s, o) {
      return r.get(`/${s}`, o);
    },
    delete(s, o) {
      return r.delete(`/${s}`, void 0, o);
    },
    update(s, o, c) {
      return r.patch(`/${s}`, o, c);
    },
    newChat(s, o, c) {
      return r.post(`/${s}/chat`, o, c);
    },
    chat(s, o, c, a) {
      return r.post(`/${s}/chat/${o}`, c, a);
    },
    createRating(s, o, c, a) {
      return r.post(`/${s}/chat/${o}/ratings`, c, a);
    },
    updateRating(s, o, c, a, i) {
      return r.patch(`/${s}/chat/${o}/ratings/${c}`, a, i);
    },
    deleteRating(s, o, c, a) {
      return r.delete(`/${s}/chat/${o}/ratings/${c}`, a);
    },
    getSTTToken(s, o) {
      return r.get(`/${s}/stt-token`, o);
    }
  };
}
const me = (e) => e.type === "clip" && e.presenter_id.startsWith("v2_") ? "clip_v2" : e.type;
function Je(e) {
  var s, o, c, a;
  const t = () => /Mobi|Android/i.test(navigator.userAgent) ? "Mobile" : "Desktop", n = () => {
    const i = navigator.platform;
    return i.toLowerCase().includes("win") ? "Windows" : i.toLowerCase().includes("mac") ? "Mac OS X" : i.toLowerCase().includes("linux") ? "Linux" : "Unknown";
  }, r = e.presenter;
  return {
    $os: `${n()}`,
    isMobile: `${t() == "Mobile"}`,
    browser: navigator.userAgent,
    origin: window.location.origin,
    agentType: me(r),
    agentVoice: {
      voiceId: (o = (s = e.presenter) == null ? void 0 : s.voice) == null ? void 0 : o.voice_id,
      provider: (a = (c = e.presenter) == null ? void 0 : c.voice) == null ? void 0 : a.type
    }
  };
}
function We(e) {
  var n, r, s, o, c, a;
  const t = (n = e.llm) == null ? void 0 : n.prompt_customization;
  return {
    agentType: me(e.presenter),
    owner_id: e.owner_id ?? "",
    promptVersion: (r = e.llm) == null ? void 0 : r.prompt_version,
    behavior: {
      role: t == null ? void 0 : t.role,
      personality: t == null ? void 0 : t.personality,
      instructions: (s = e.llm) == null ? void 0 : s.instructions
    },
    temperature: (o = e.llm) == null ? void 0 : o.temperature,
    knowledgeSource: t == null ? void 0 : t.knowledge_source,
    starterQuestionsCount: (a = (c = e.knowledge) == null ? void 0 : c.starter_message) == null ? void 0 : a.length,
    topicsToAvoid: t == null ? void 0 : t.topics_to_avoid,
    maxResponseLength: t == null ? void 0 : t.max_response_length,
    agentId: e.id,
    access: e.access,
    name: e.preview_name,
    ...e.access === "public" ? {
      from: "agent-template"
    } : {}
  };
}
const Ue = (e) => e.reduce((t, n) => t + n, 0), ae = (e) => Ue(e) / e.length;
function Ke(e, t, n) {
  var i, d, l;
  const {
    event: r,
    ...s
  } = e, {
    template: o
  } = (t == null ? void 0 : t.llm) || {}, {
    language: c
  } = ((i = t == null ? void 0 : t.presenter) == null ? void 0 : i.voice) || {};
  return {
    ...s,
    llm: {
      ...s.llm,
      template: o
    },
    script: {
      ...s.script,
      provider: {
        ...(d = s == null ? void 0 : s.script) == null ? void 0 : d.provider,
        language: c
      }
    },
    stitch: (t == null ? void 0 : t.presenter.type) === "talk" ? (l = t == null ? void 0 : t.presenter) == null ? void 0 : l.stitch : void 0,
    ...n
  };
}
let ee = {};
const He = "https://api-js.mixpanel.com/track/?verbose=1&ip=1";
function qe(e) {
  const t = window != null && window.hasOwnProperty("DID_AGENTS_API") ? "agents-ui" : "agents-sdk";
  return {
    token: e.token || "testKey",
    distinct_id: e.distinctId || de(),
    agentId: e.agentId,
    additionalProperties: {},
    isEnabled: e.isEnabled ?? !0,
    getRandom: V,
    enrich(n) {
      this.additionalProperties = {
        ...this.additionalProperties,
        ...n
      };
    },
    async track(n, r) {
      if (!this.isEnabled)
        return Promise.resolve();
      const {
        audioPath: s,
        ...o
      } = r || {}, c = {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded"
        },
        body: new URLSearchParams({
          data: JSON.stringify([{
            event: n,
            properties: {
              ...this.additionalProperties,
              ...o,
              agentId: this.agentId,
              source: t,
              token: this.token,
              time: Date.now(),
              $insert_id: this.getRandom(),
              origin: window.location.href,
              "Screen Height": window.screen.height || window.innerWidth,
              "Screen Width": window.screen.width || window.innerHeight,
              "User Agent": navigator.userAgent
            }
          }])
        })
      };
      try {
        return await fetch(He, c).then((a) => a.json());
      } catch (a) {
        return console.error(a);
      }
    },
    linkTrack(n, r, s, o) {
      ee[n] || (ee[n] = {
        events: {},
        resolvedDependencies: []
      }), o.includes(s) || o.push(s);
      const c = ee[n];
      if (c.events[s] = {
        props: r
      }, c.resolvedDependencies.push(s), o.every((i) => c.resolvedDependencies.includes(i))) {
        const i = o.reduce((d, l) => c.events[l] ? {
          ...d,
          ...c.events[l].props
        } : d, {});
        this.track(n, i), c.resolvedDependencies = c.resolvedDependencies.filter((d) => !o.includes(d)), o.forEach((d) => {
          delete c.events[d];
        });
      }
    }
  };
}
function ge() {
  let e = 0;
  return {
    reset: () => e = 0,
    update: () => e = Date.now(),
    get: (t = !1) => t ? Date.now() - e : e
  };
}
const N = ge(), re = ge();
function he(e) {
  return e === M.Playground ? {
    headers: {
      [Be]: "true"
    }
  } : {};
}
async function we(e, t, n, r, s = !1, o) {
  try {
    return !o && !ce(r) && (o = await t.newChat(e.id, {
      persist: s
    }, he(r)), n.track("agent-chat", {
      event: "created",
      chatId: o.id,
      mode: r
    })), {
      chat: o,
      chatMode: (o == null ? void 0 : o.chat_mode) ?? r
    };
  } catch (c) {
    try {
      const a = JSON.parse(c.message);
      if ((a == null ? void 0 : a.kind) === "InsufficientCreditsError")
        throw new Error("InsufficientCreditsError");
    } catch (a) {
      console.error("Error parsing the error message:", a);
    }
    throw new Error("Cannot create new chat");
  }
}
function Ve(e) {
  return e && e.length > 0 ? e : [];
}
function Oe(e, t, n) {
  if (!e)
    throw new Error("Please connect to the agent first");
  if (!e.interruptAvailable)
    throw new Error("Interrupt is not enabled for this stream");
  if (t !== W.Fluent)
    throw new Error("Interrupt only available for Fluent streams");
  if (!n)
    throw new Error("No active video to interrupt");
}
async function Xe(e, t) {
  const n = {
    type: A.StreamInterrupt,
    videoId: t,
    timestamp: Date.now()
  };
  e.sendDataChannelMessage(JSON.stringify(n));
}
function Ye(e) {
  return new Promise((t, n) => {
    const {
      callbacks: r,
      host: s,
      auth: o
    } = e, {
      onMessage: c = null,
      onOpen: a = null,
      onClose: i = null,
      onError: d = null
    } = r || {}, l = new WebSocket(`${s}?authorization=${le(o)}`);
    l.onmessage = c, l.onclose = i, l.onerror = (y) => {
      console.error(y), d == null || d("Websocket failed to connect", y), n(y);
    }, l.onopen = (y) => {
      a == null || a(y), t(l);
    };
  });
}
async function Qe(e) {
  const {
    retries: t = 1
  } = e;
  let n = null;
  for (let r = 0; (n == null ? void 0 : n.readyState) !== WebSocket.OPEN; r++)
    try {
      n = await Ye(e);
    } catch (s) {
      if (r === t)
        throw s;
      await oe(r * 500);
    }
  return n;
}
async function Ze(e, t, n) {
  const r = n != null && n.onMessage ? [n.onMessage] : [], s = await Qe({
    auth: e,
    host: t,
    callbacks: {
      onError: (o) => {
        var c;
        return (c = n.onError) == null ? void 0 : c.call(n, new Ce(o));
      },
      onMessage(o) {
        const c = JSON.parse(o.data);
        r.forEach((a) => a(c.event, c));
      }
    }
  });
  return {
    socket: s,
    disconnect: () => s.close(),
    subscribeToEvents: (o) => r.push(o)
  };
}
function Ge(e) {
  if (e.answer !== void 0)
    return e.answer;
  let t = 0, n = "";
  for (; t in e; )
    n += e[t++];
  return n;
}
function et(e, t, n, r, s) {
  const o = r.messages[r.messages.length - 1];
  if (!(e === q.Partial || e === q.Answer) || (o == null ? void 0 : o.role) !== "assistant")
    return;
  const {
    content: c,
    sequence: a
  } = t;
  e === q.Partial ? n[a] = c : n.answer = c;
  const i = Ge(n);
  (o.content !== i || e === q.Answer) && (o.content = i, s == null || s([...r.messages], e));
}
function tt(e, t, n, r, s) {
  let o = {};
  return {
    clearQueue: () => o = {},
    onMessage: (c, a) => {
      var i, d;
      if ("content" in a)
        et(c, a, o, t, n.callbacks.onNewMessage), c === q.Answer && e.track("agent-message-received", {
          messages: t.messages.length,
          mode: t.chatMode
        });
      else {
        const l = A, y = [l.StreamVideoDone, l.StreamVideoError, l.StreamVideoRejected], _ = [l.StreamFailed, l.StreamVideoError, l.StreamVideoRejected], T = Ke(a, r, {
          mode: t.chatMode
        });
        if (c = c, c === l.StreamVideoCreated)
          e.linkTrack("agent-video", T, l.StreamVideoCreated, ["start"]);
        else if (y.includes(c)) {
          const j = c.split("/")[1];
          _.includes(c) ? e.track("agent-video", {
            ...T,
            event: j
          }) : e.linkTrack("agent-video", {
            ...T,
            event: j
          }, c, ["done"]);
        }
        _.includes(c) && ((d = (i = n.callbacks).onError) == null || d.call(i, new Error(`Stream failed with event ${c}`), {
          data: a
        })), a.event === l.StreamDone && s();
      }
    }
  };
}
function rt(e, t, n, r) {
  const s = ue(e, `${t}/agents/${n}`, r);
  return {
    createStream(o) {
      return s.post("/streams", o);
    },
    startConnection(o, c, a) {
      return s.post(`/streams/${o}/sdp`, {
        session_id: a,
        answer: c
      });
    },
    addIceCandidate(o, c, a) {
      return s.post(`/streams/${o}/ice`, {
        session_id: a,
        ...c
      });
    },
    sendStreamRequest(o, c, a) {
      return s.post(`/streams/${o}`, {
        session_id: c,
        ...a
      });
    },
    close(o, c) {
      return s.delete(`/streams/${o}`, {
        session_id: c
      });
    }
  };
}
function nt(e, t, n) {
  const r = (t.timestamp - e.timestamp) / 1e3;
  return {
    duration: r,
    bytesReceived: t.bytesReceived - e.bytesReceived,
    bitrate: Math.round((t.bytesReceived - e.bytesReceived) * 8 / r),
    packetsReceived: t.packetsReceived - e.packetsReceived,
    packetsLost: t.packetsLost - e.packetsLost,
    framesDropped: t.framesDropped - e.framesDropped,
    framesDecoded: t.framesDecoded - e.framesDecoded,
    jitter: t.jitter,
    avgJitterDelayInInterval: (t.jitterBufferDelay - e.jitterBufferDelay) / (t.jitterBufferEmittedCount - e.jitterBufferEmittedCount),
    jitterBufferEmittedCount: t.jitterBufferEmittedCount - e.jitterBufferEmittedCount,
    jitterBufferDelay: (t.jitterBufferDelay - e.jitterBufferDelay) / r,
    framesPerSecond: t.framesPerSecond,
    freezeCount: t.freezeCount - e.freezeCount,
    freezeDuration: t.freezeDuration - e.freezeDuration,
    lowFpsCount: n
  };
}
function at(e) {
  return e.filter((t) => t.freezeCount > 0 || t.framesPerSecond < 21 || t.framesDropped > 0 || t.packetsLost > 0).map((t) => {
    const {
      timestamp: n,
      ...r
    } = t, s = [];
    return t.freezeCount > 0 && s.push("freeze"), t.framesPerSecond < 21 && s.push("low fps"), t.framesDropped > 0 && s.push("frames dropped"), t.packetsLost > 0 && s.push("packet loss"), {
      ...r,
      causes: s
    };
  });
}
function it(e) {
  let t = "", n = 0;
  for (const r of e.values())
    if (r && r.type === "codec" && r.mimeType.startsWith("video") && (t = r.mimeType.split("/")[1]), r && r.type === "candidate-pair" && (n = r.currentRoundTripTime), r && r.type === "inbound-rtp" && r.kind === "video")
      return {
        codec: t,
        rtt: n,
        timestamp: r.timestamp,
        bytesReceived: r.bytesReceived,
        packetsReceived: r.packetsReceived,
        packetsLost: r.packetsLost,
        framesDropped: r.framesDropped,
        framesDecoded: r.framesDecoded,
        jitter: r.jitter,
        jitterBufferDelay: r.jitterBufferDelay,
        jitterBufferEmittedCount: r.jitterBufferEmittedCount,
        avgJitterDelayInInterval: r.jitterBufferDelay / r.jitterBufferEmittedCount,
        frameWidth: r.frameWidth,
        frameHeight: r.frameHeight,
        framesPerSecond: r.framesPerSecond,
        freezeCount: r.freezeCount,
        freezeDuration: r.totalFreezesDuration
      };
  return {};
}
function st(e, t, n) {
  const r = e.map((i, d) => d === 0 ? n ? {
    timestamp: i.timestamp,
    duration: 0,
    rtt: i.rtt,
    bytesReceived: i.bytesReceived - n.bytesReceived,
    bitrate: (i.bytesReceived - n.bytesReceived) * 8 / (t / 1e3),
    packetsReceived: i.packetsReceived - n.packetsReceived,
    packetsLost: i.packetsLost - n.packetsLost,
    framesDropped: i.framesDropped - n.framesDropped,
    framesDecoded: i.framesDecoded - n.framesDecoded,
    jitter: i.jitter,
    jitterBufferDelay: i.jitterBufferDelay - n.jitterBufferDelay,
    jitterBufferEmittedCount: i.jitterBufferEmittedCount - n.jitterBufferEmittedCount,
    avgJitterDelayInInterval: (i.jitterBufferDelay - n.jitterBufferDelay) / (i.jitterBufferEmittedCount - n.jitterBufferEmittedCount),
    framesPerSecond: i.framesPerSecond,
    freezeCount: i.freezeCount - n.freezeCount,
    freezeDuration: i.freezeDuration - n.freezeDuration
  } : {
    timestamp: i.timestamp,
    rtt: i.rtt,
    duration: 0,
    bytesReceived: i.bytesReceived,
    bitrate: i.bytesReceived * 8 / (t / 1e3),
    packetsReceived: i.packetsReceived,
    packetsLost: i.packetsLost,
    framesDropped: i.framesDropped,
    framesDecoded: i.framesDecoded,
    jitter: i.jitter,
    jitterBufferDelay: i.jitterBufferDelay,
    jitterBufferEmittedCount: i.jitterBufferEmittedCount,
    avgJitterDelayInInterval: i.jitterBufferDelay / i.jitterBufferEmittedCount,
    framesPerSecond: i.framesPerSecond,
    freezeCount: i.freezeCount,
    freezeDuration: i.freezeDuration
  } : {
    timestamp: i.timestamp,
    duration: t * d / 1e3,
    rtt: i.rtt,
    bytesReceived: i.bytesReceived - e[d - 1].bytesReceived,
    bitrate: (i.bytesReceived - e[d - 1].bytesReceived) * 8 / (t / 1e3),
    packetsReceived: i.packetsReceived - e[d - 1].packetsReceived,
    packetsLost: i.packetsLost - e[d - 1].packetsLost,
    framesDropped: i.framesDropped - e[d - 1].framesDropped,
    framesDecoded: i.framesDecoded - e[d - 1].framesDecoded,
    jitter: i.jitter,
    jitterBufferDelay: i.jitterBufferDelay - e[d - 1].jitterBufferDelay,
    jitterBufferEmittedCount: i.jitterBufferEmittedCount - e[d - 1].jitterBufferEmittedCount,
    avgJitterDelayInInterval: (i.jitterBufferDelay - e[d - 1].jitterBufferDelay) / (i.jitterBufferEmittedCount - e[d - 1].jitterBufferEmittedCount),
    framesPerSecond: i.framesPerSecond,
    freezeCount: i.freezeCount - e[d - 1].freezeCount,
    freezeDuration: i.freezeDuration - e[d - 1].freezeDuration
  }), s = at(r), o = s.reduce((i, d) => i + (d.causes.includes("low fps") ? 1 : 0), 0), c = r.filter((i) => !!i.avgJitterDelayInInterval).map((i) => i.avgJitterDelayInInterval), a = r.filter((i) => !!i.rtt).map((i) => i.rtt);
  return {
    webRTCStats: {
      anomalies: s,
      minRtt: Math.min(...a),
      avgRtt: ae(a),
      maxRtt: Math.max(...a),
      aggregateReport: nt(e[0], e[e.length - 1], o),
      minJitterDelayInInterval: Math.min(...c),
      maxJitterDelayInInterval: Math.max(...c),
      avgJitterDelayInInterval: ae(c)
    },
    codec: e[0].codec,
    resolution: `${e[0].frameWidth}x${e[0].frameHeight}`
  };
}
const ne = 100, ot = Math.max(Math.ceil(400 / ne), 1), ct = 0.25, dt = 0.28;
function lt() {
  let e = 0, t, n, r = 0;
  return (s) => {
    for (const o of s.values())
      if (o && o.type === "inbound-rtp" && o.kind === "video") {
        const c = o.jitterBufferDelay, a = o.jitterBufferEmittedCount;
        if (n && a > n) {
          const l = c - t, y = a - n;
          r = l / y;
        }
        t = c, n = a;
        const i = o.framesDecoded, d = i - e > 0;
        return e = i, {
          isReceiving: d,
          avgJitterDelayInInterval: r,
          freezeCount: o.freezeCount
        };
      }
    return {
      isReceiving: !1,
      avgJitterDelayInInterval: r
    };
  };
}
function ut(e, t, n, r, s, o = !1) {
  let c = [], a, i = 0, d = !1, l = Q.Unknown, y = Q.Unknown, _ = 0, T = 0;
  const j = lt();
  return setInterval(async () => {
    const x = await e.getStats(), {
      isReceiving: R,
      avgJitterDelayInInterval: U,
      freezeCount: K
    } = j(x), P = it(x);
    if (R)
      i = 0, _ = K - T, y = U < ct ? Q.Strong : U > dt && _ > 1 ? Q.Weak : l, y !== l && (s == null || s(y), l = y, T += _, _ = 0), d || (r == null || r(h.Start), a = c[c.length - 1], c = [], d = !0), c.push(P);
    else if (d && (i++, i >= ot)) {
      const u = st(c, ne, a);
      r == null || r(h.Stop, u), t() || n(), T = K, d = !1;
    }
  }, ne);
}
let pe = !1;
const B = (e, t) => pe && console.log(e, t), ft = (window.RTCPeerConnection || window.webkitRTCPeerConnection || window.mozRTCPeerConnection).bind(window);
function ie(e) {
  switch (e) {
    case "connected":
      return I.Connected;
    case "checking":
      return I.Connecting;
    case "failed":
      return I.Fail;
    case "new":
      return I.New;
    case "closed":
      return I.Closed;
    case "disconnected":
      return I.Disconnected;
    case "completed":
      return I.Completed;
    default:
      return I.New;
  }
}
function mt(e) {
  const [t, n = ""] = e.split(/:(.+)/);
  try {
    const r = JSON.parse(n);
    return B("parsed data channel message", {
      subject: t,
      data: r
    }), {
      subject: t,
      data: r
    };
  } catch (r) {
    return B("Failed to parse data channel message, returning data as string", {
      subject: t,
      rawData: n,
      error: r
    }), {
      subject: t,
      data: n
    };
  }
}
function gt({
  statsSignal: e,
  dataChannelSignal: t,
  onVideoStateChange: n,
  report: r
}) {
  e === h.Start && t === h.Start ? n == null || n(h.Start) : e === h.Stop && t === h.Stop && (n == null || n(h.Stop, r));
}
function ht({
  statsSignal: e,
  dataChannelSignal: t,
  onVideoStateChange: n,
  onAgentActivityStateChange: r,
  report: s
}) {
  e === h.Start ? n == null || n(h.Start) : e === h.Stop && (n == null || n(h.Stop, s)), t === h.Start ? r == null || r(O.Talking) : t === h.Stop && (r == null || r(O.Idle));
}
function se({
  statsSignal: e,
  dataChannelSignal: t,
  onVideoStateChange: n,
  onAgentActivityStateChange: r,
  streamType: s,
  report: o
}) {
  s === W.Legacy ? gt({
    statsSignal: e,
    dataChannelSignal: t,
    onVideoStateChange: n,
    report: o
  }) : s === W.Fluent && ht({
    statsSignal: e,
    dataChannelSignal: t,
    onVideoStateChange: n,
    onAgentActivityStateChange: r,
    report: o
  });
}
async function wt(e, t, {
  debug: n = !1,
  callbacks: r,
  auth: s,
  baseURL: o = Z,
  analytics: c
}) {
  var J;
  pe = n;
  let a = !1, i = !1, d = h.Stop, l = h.Stop;
  const {
    startConnection: y,
    sendStreamRequest: _,
    close: T,
    createStream: j,
    addIceCandidate: x
  } = rt(s, o, e, r.onError), {
    id: R,
    offer: U,
    ice_servers: K,
    session_id: P,
    fluent: u,
    interrupt_enabled: v
  } = await j(t);
  (J = r.onStreamCreated) == null || J.call(r, {
    stream_id: R,
    session_id: P,
    agent_id: e
  });
  const m = new ft({
    iceServers: K
  }), D = m.createDataChannel("JanusDataChannel");
  if (!P)
    throw new Error("Could not create session_id");
  const w = u ? W.Fluent : W.Legacy;
  c.enrich({
    "stream-type": w
  });
  const p = t.stream_warmup && !u, b = () => a, L = () => {
    var f;
    a = !0, i && ((f = r.onConnectionStateChange) == null || f.call(r, I.Connected));
  }, $ = ut(m, b, L, (f, g) => se({
    statsSignal: l = f,
    dataChannelSignal: w === W.Legacy ? d : void 0,
    onVideoStateChange: r.onVideoStateChange,
    onAgentActivityStateChange: r.onAgentActivityStateChange,
    report: g,
    streamType: w
  }), (f) => {
    var g;
    return (g = r.onConnectivityStateChange) == null ? void 0 : g.call(r, f);
  }, p);
  m.onicecandidate = (f) => {
    var g;
    B("peerConnection.onicecandidate", f);
    try {
      f.candidate && f.candidate.sdpMid && f.candidate.sdpMLineIndex !== null ? x(R, {
        candidate: f.candidate.candidate,
        sdpMid: f.candidate.sdpMid,
        sdpMLineIndex: f.candidate.sdpMLineIndex
      }, P) : x(R, {
        candidate: null
      }, P);
    } catch (S) {
      (g = r.onError) == null || g.call(r, S, {
        streamId: R
      });
    }
  }, D.onopen = () => {
    i = !0, (!p || a) && L();
  };
  const k = (f) => {
    var g;
    (g = r.onVideoIdChange) == null || g.call(r, f);
  };
  function E(f, g) {
    if (f === A.StreamStarted && typeof g == "object" && "metadata" in g) {
      const S = g.metadata;
      k(S.videoId);
    }
    f === A.StreamDone && k(null), d = f === A.StreamStarted ? h.Start : h.Stop, se({
      statsSignal: w === W.Legacy ? l : void 0,
      dataChannelSignal: d,
      onVideoStateChange: r.onVideoStateChange,
      onAgentActivityStateChange: r.onAgentActivityStateChange,
      streamType: w
    });
  }
  function C(f, g) {
    const S = typeof g == "string" ? g : g == null ? void 0 : g.metadata;
    S && c.enrich({
      streamMetadata: S
    }), c.track("agent-chat", {
      event: "ready"
    });
  }
  const z = {
    [A.StreamStarted]: E,
    [A.StreamDone]: E,
    [A.StreamReady]: C
  };
  D.onmessage = (f) => {
    var X;
    const {
      subject: g,
      data: S
    } = mt(f.data);
    (X = z[g]) == null || X.call(z, g, S);
  }, m.oniceconnectionstatechange = () => {
    var g;
    B("peerConnection.oniceconnectionstatechange => " + m.iceConnectionState);
    const f = ie(m.iceConnectionState);
    f !== I.Connected && ((g = r.onConnectionStateChange) == null || g.call(r, f));
  }, m.ontrack = (f) => {
    var g;
    B("peerConnection.ontrack", f), (g = r.onSrcObjectReady) == null || g.call(r, f.streams[0]);
  }, await m.setRemoteDescription(U), B("set remote description OK");
  const F = await m.createAnswer();
  return B("create answer OK"), await m.setLocalDescription(F), B("set local description OK"), await y(R, F, P), B("start connection OK"), {
    /**
     * Method to send request to server to get clip or talk depend on you payload
     * @param payload
     */
    speak(f) {
      return _(R, P, f);
    },
    /**
     * Method to close RTC connection
     */
    async disconnect() {
      var f;
      if (R) {
        const g = ie(m.iceConnectionState);
        if (m) {
          if (g === I.New) {
            clearInterval($);
            return;
          }
          m.close(), m.oniceconnectionstatechange = null, m.onnegotiationneeded = null, m.onicecandidate = null, m.ontrack = null;
        }
        try {
          g === I.Connected && await T(R, P).catch((S) => {
          });
        } catch (S) {
          B("Error on close stream connection", S);
        }
        (f = r.onAgentActivityStateChange) == null || f.call(r, O.Idle), clearInterval($);
      }
    },
    /**
     * Method to send data channel messages to the server
     */
    sendDataChannelMessage(f) {
      var g, S;
      if (!a || D.readyState !== "open") {
        B("Data channel is not ready for sending messages"), (g = r.onError) == null || g.call(r, new Error("Data channel is not ready for sending messages"), {
          streamId: R
        });
        return;
      }
      try {
        D.send(f);
      } catch (X) {
        B("Error sending data channel message", X), (S = r.onError) == null || S.call(r, X, {
          streamId: R
        });
      }
    },
    /**
     * Session identifier information, should be returned in the body of all streaming requests
     */
    sessionId: P,
    /**
     * Id of current RTC stream
     */
    streamId: R,
    streamType: w,
    interruptAvailable: v
  };
}
function pt(e) {
  const {
    streamOptions: t
  } = e ?? {};
  return {
    output_resolution: t == null ? void 0 : t.outputResolution,
    session_timeout: t == null ? void 0 : t.sessionTimeout,
    stream_warmup: t == null ? void 0 : t.streamWarmup,
    compatibility_mode: t == null ? void 0 : t.compatibilityMode,
    fluent: t == null ? void 0 : t.fluent
  };
}
function yt(e, t, n, r, s) {
  s === W.Fluent ? vt(e, t, n, r, s) : Dt(e, t, n, r, s);
}
function vt(e, t, n, r, s) {
  e === h.Start ? r.track("stream-session", {
    event: "start",
    "stream-type": s
  }) : e === h.Stop && r.track("stream-session", {
    event: "stop",
    is_greenscreen: t.presenter.type === "clip" && t.presenter.is_greenscreen,
    background: t.presenter.type === "clip" && t.presenter.background,
    "stream-type": s,
    ...n
  });
}
function kt(e, t, n, r) {
  N.get() <= 0 || (e === h.Start ? n.linkTrack("agent-video", {
    event: "start",
    latency: N.get(!0),
    "stream-type": r
  }, "start", [A.StreamVideoCreated]) : e === h.Stop && n.linkTrack("agent-video", {
    event: "stop",
    is_greenscreen: t.presenter.type === "clip" && t.presenter.is_greenscreen,
    background: t.presenter.type === "clip" && t.presenter.background,
    "stream-type": r
  }, "done", [A.StreamVideoDone]));
}
function Dt(e, t, n, r, s) {
  N.get() <= 0 || (e === h.Start ? r.linkTrack("agent-video", {
    event: "start",
    latency: N.get(!0),
    "stream-type": s
  }, "start", [A.StreamVideoCreated]) : e === h.Stop && r.linkTrack("agent-video", {
    event: "stop",
    is_greenscreen: t.presenter.type === "clip" && t.presenter.is_greenscreen,
    background: t.presenter.type === "clip" && t.presenter.background,
    "stream-type": s,
    ...n
  }, "done", [A.StreamVideoDone]));
}
function Ct(e, t, n) {
  return N.reset(), new Promise(async (r, s) => {
    try {
      const o = await wt(e.id, pt(t), {
        ...t,
        analytics: n,
        callbacks: {
          ...t.callbacks,
          onConnectionStateChange: (c) => {
            var a, i;
            (i = (a = t.callbacks).onConnectionStateChange) == null || i.call(a, c), c === I.Connected && r(o);
          },
          onVideoStateChange: (c, a) => {
            var i, d;
            (d = (i = t.callbacks).onVideoStateChange) == null || d.call(i, c), yt(c, e, a, n, o.streamType);
          },
          onAgentActivityStateChange: (c) => {
            var a, i;
            (i = (a = t.callbacks).onAgentActivityStateChange) == null || i.call(a, c), c === O.Talking ? re.update() : re.reset(), kt(c === O.Talking ? h.Start : h.Stop, e, n, o.streamType);
          }
        }
      });
    } catch (o) {
      s(o);
    }
  });
}
async function Rt(e, t, n, r, s) {
  var y, _, T, j;
  const o = we(e, n, r, t.mode, t.persistentChat, s), c = Ct(e, t, r), [a, i] = await Promise.all([o, c]), {
    chat: d,
    chatMode: l
  } = a;
  return l && l !== t.mode && (t.mode = l, (_ = (y = t.callbacks).onModeChange) == null || _.call(y, l), l !== M.Functional) ? ((j = (T = t.callbacks).onError) == null || j.call(T, new De(l)), i == null || i.disconnect(), {
    chat: d
  }) : {
    chat: d,
    streamingManager: i
  };
}
async function Et(e, t) {
  var U, K, P;
  let n = !0, r = null;
  const s = t.mixpanelKey || $e, o = t.wsURL || Le, c = t.baseURL || Z, a = {
    messages: [],
    chatMode: t.mode || M.Functional
  }, i = qe({
    token: s,
    agentId: e,
    isEnabled: t.enableAnalitics,
    distinctId: t.distinctId
  });
  i.track("agent-sdk", {
    event: "init"
  });
  const d = fe(t.auth, c, t.callbacks.onError), l = await d.getById(e);
  i.enrich(We(l));
  const {
    onMessage: y,
    clearQueue: _
  } = tt(i, a, t, l, () => {
    var u;
    return (u = a.socketManager) == null ? void 0 : u.disconnect();
  });
  a.messages = Ve(t.initialMessages), (K = (U = t.callbacks).onNewMessage) == null || K.call(U, [...a.messages], "answer");
  const T = (u) => {
    r = u;
  };
  i.track("agent-sdk", {
    event: "loaded",
    ...Je(l)
  });
  async function j(u) {
    var b, L, $, k, E, C, z;
    (L = (b = t.callbacks).onConnectionStateChange) == null || L.call(b, I.Connecting), N.reset(), u && !n && (delete a.chat, (k = ($ = t.callbacks).onNewMessage) == null || k.call($, [...a.messages], "answer"));
    const v = t.mode === M.DirectPlayback ? Promise.resolve(void 0) : Ze(t.auth, o, {
      onMessage: y,
      onError: t.callbacks.onError
    }), m = te(() => Rt(l, {
      ...t,
      callbacks: {
        ...t.callbacks,
        onVideoIdChange: T
      }
    }, d, i, a.chat), {
      limit: 3,
      timeout: Ae,
      timeoutErrorMessage: "Timeout initializing the stream",
      // Retry on all errors except for connection errors and rate limit errors, these are already handled in client level.
      shouldRetryFn: (F) => (F == null ? void 0 : F.message) !== "Could not connect" && F.status !== 429,
      delayMs: 1e3
    }).catch((F) => {
      var J, f;
      throw R(M.Maintenance), (f = (J = t.callbacks).onConnectionStateChange) == null || f.call(J, I.Fail), F;
    }), [D, {
      streamingManager: w,
      chat: p
    }] = await Promise.all([v, m]);
    p && p.id !== ((E = a.chat) == null ? void 0 : E.id) && ((z = (C = t.callbacks).onNewChat) == null || z.call(C, p.id)), a.streamingManager = w, a.socketManager = D, a.chat = p, n = !1, i.enrich({
      chatId: p == null ? void 0 : p.id,
      streamId: w == null ? void 0 : w.streamId,
      mode: a.chatMode
    }), R((p == null ? void 0 : p.chat_mode) ?? t.mode ?? M.Functional);
  }
  async function x() {
    var u, v, m, D;
    (u = a.socketManager) == null || u.disconnect(), await ((v = a.streamingManager) == null ? void 0 : v.disconnect()), delete a.streamingManager, delete a.socketManager, (D = (m = t.callbacks).onConnectionStateChange) == null || D.call(m, I.Disconnected);
  }
  async function R(u) {
    var v, m;
    u !== a.chatMode && (i.track("agent-mode-change", {
      mode: u
    }), a.chatMode = u, a.chatMode !== M.Functional && await x(), (m = (v = t.callbacks).onModeChange) == null || m.call(v, u));
  }
  return {
    agent: l,
    getStreamType: () => {
      var u;
      return (u = a.streamingManager) == null ? void 0 : u.streamType;
    },
    getIsInterruptAvailable: () => {
      var u;
      return ((u = a.streamingManager) == null ? void 0 : u.interruptAvailable) ?? !1;
    },
    starterMessages: ((P = l.knowledge) == null ? void 0 : P.starter_message) || [],
    getSTTToken: () => d.getSTTToken(l.id),
    changeMode: R,
    enrichAnalytics: i.enrich,
    async connect() {
      await j(!0), i.track("agent-chat", {
        event: "connect",
        mode: a.chatMode
      });
    },
    async reconnect() {
      await x(), await j(!1), i.track("agent-chat", {
        event: "reconnect",
        mode: a.chatMode
      });
    },
    async disconnect() {
      await x(), i.track("agent-chat", {
        event: "disconnect",
        mode: a.chatMode
      });
    },
    async chat(u) {
      var w, p, b, L, $;
      const v = () => {
        if (ce(t.mode))
          throw new H(`${t.mode} is enabled, chat is disabled`);
        if (u.length >= 800)
          throw new H("Message cannot be more than 800 characters");
        if (u.length === 0)
          throw new H("Message cannot be empty");
        if (a.chatMode === M.Maintenance)
          throw new H("Chat is in maintenance mode");
        if (![M.TextOnly, M.Playground].includes(a.chatMode)) {
          if (!a.streamingManager)
            throw new H("Streaming manager is not initialized");
          if (!a.chat)
            throw new H("Chat is not initialized");
        }
      }, m = async () => {
        var k, E;
        if (!a.chat) {
          const C = await we(l, d, i, a.chatMode, t.persistentChat);
          if (!C.chat)
            throw new ke(a.chatMode, !!t.persistentChat);
          a.chat = C.chat, (E = (k = t.callbacks).onNewChat) == null || E.call(k, a.chat.id);
        }
        return a.chat.id;
      }, D = async (k, E) => te(() => {
        var C, z;
        return d.chat(l.id, E, {
          chatMode: a.chatMode,
          streamId: (C = a.streamingManager) == null ? void 0 : C.streamId,
          sessionId: (z = a.streamingManager) == null ? void 0 : z.sessionId,
          messages: k.map(({
            matches: F,
            ...J
          }) => J)
        }, {
          ...he(a.chatMode),
          skipErrorHandler: !0
        });
      }, {
        limit: 2,
        shouldRetryFn: (C) => {
          var J, f, g, S;
          const z = (J = C == null ? void 0 : C.message) == null ? void 0 : J.includes("missing or invalid session_id");
          return !((f = C == null ? void 0 : C.message) == null ? void 0 : f.includes("Stream Error")) && !z ? ((S = (g = t.callbacks).onError) == null || S.call(g, C), !1) : !0;
        },
        onRetry: async () => {
          await x(), await j(!1);
        }
      });
      try {
        _(), v(), a.messages.push({
          id: V(),
          role: "user",
          content: u,
          created_at: new Date(N.update()).toISOString()
        }), (p = (w = t.callbacks).onNewMessage) == null || p.call(w, [...a.messages], "user");
        const k = await m(), E = await D([...a.messages], k);
        return a.messages.push({
          id: V(),
          role: "assistant",
          content: E.result || "",
          created_at: (/* @__PURE__ */ new Date()).toISOString(),
          context: E.context,
          matches: E.matches
        }), i.track("agent-message-send", {
          event: "success",
          messages: a.messages.length + 1
        }), E.result && ((L = (b = t.callbacks).onNewMessage) == null || L.call(b, [...a.messages], "answer"), i.track("agent-message-received", {
          latency: N.get(!0),
          messages: a.messages.length
        })), E;
      } catch (k) {
        throw (($ = a.messages[a.messages.length - 1]) == null ? void 0 : $.role) === "assistant" && a.messages.pop(), i.track("agent-message-send", {
          event: "error",
          messages: a.messages.length
        }), k;
      }
    },
    rate(u, v, m) {
      var p, b, L, $;
      const D = a.messages.find((k) => k.id === u);
      if (a.chat) {
        if (!D)
          throw new Error("Message not found");
      } else
        throw new Error("Chat is not initialized");
      const w = ((p = D.matches) == null ? void 0 : p.map((k) => [k.document_id, k.id])) ?? [];
      return i.track("agent-rate", {
        event: m ? "update" : "create",
        thumb: v === 1 ? "up" : "down",
        knowledge_id: ((b = l.knowledge) == null ? void 0 : b.id) ?? "",
        matches: w,
        score: v
      }), m ? d.updateRating(l.id, a.chat.id, m, {
        knowledge_id: ((L = l.knowledge) == null ? void 0 : L.id) ?? "",
        message_id: u,
        matches: w,
        score: v
      }) : d.createRating(l.id, a.chat.id, {
        knowledge_id: (($ = l.knowledge) == null ? void 0 : $.id) ?? "",
        message_id: u,
        matches: w,
        score: v
      });
    },
    deleteRate(u) {
      if (!a.chat)
        throw new Error("Chat is not initialized");
      return i.track("agent-rate-delete", {
        type: "text"
      }), d.deleteRating(l.id, a.chat.id, u);
    },
    async speak(u) {
      var w, p, b;
      function v() {
        if (typeof u == "string") {
          if (!l.presenter.voice)
            throw new Error("Presenter voice is not initialized");
          return {
            type: "text",
            provider: l.presenter.voice,
            input: u,
            ssml: !1
          };
        }
        if (u.type === "text" && !u.provider) {
          if (!l.presenter.voice)
            throw new Error("Presenter voice is not initialized");
          return {
            type: "text",
            provider: l.presenter.voice,
            input: u.input,
            ssml: u.ssml
          };
        }
        return u;
      }
      const m = v();
      if (i.track("agent-speak", m), N.update(), a.messages && m.type === "text" && (a.messages.push({
        id: V(),
        role: "assistant",
        content: m.input,
        created_at: new Date(N.get(!0)).toISOString()
      }), (p = (w = t.callbacks).onNewMessage) == null || p.call(w, [...a.messages], "answer")), ze(a.chatMode))
        return {
          duration: 0,
          video_id: "",
          status: "success"
        };
      if (!a.streamingManager)
        throw new Error("Please connect to the agent first");
      return a.streamingManager.speak({
        script: m,
        metadata: {
          chat_id: (b = a.chat) == null ? void 0 : b.id,
          agent_id: l.id
        }
      });
    },
    async interrupt({
      type: u
    }) {
      var m, D, w;
      Oe(a.streamingManager, (m = a.streamingManager) == null ? void 0 : m.streamType, r);
      const v = a.messages[a.messages.length - 1];
      i.track("agent-video-interrupt", {
        type: u || "click",
        video_duration_to_interrupt: re.get(!0),
        message_duration_to_interrupt: N.get(!0)
      }), v.interrupted = !0, (w = (D = t.callbacks).onNewMessage) == null || w.call(D, [...a.messages], "answer"), Xe(a.streamingManager, r);
    }
  };
}
function Mt(e, t, n) {
  const {
    getById: r
  } = fe(t, n || Z);
  return r(e);
}
export {
  O as AgentActivityState,
  Ie as AgentStatus,
  ke as ChatCreationFailed,
  M as ChatMode,
  De as ChatModeDowngraded,
  q as ChatProgress,
  I as ConnectionState,
  Q as ConnectivityState,
  Te as DocumentType,
  _e as KnowledgeType,
  Se as PlanGroup,
  Pe as Providers,
  Ee as RateState,
  A as StreamEvents,
  W as StreamType,
  h as StreamingState,
  Me as Subject,
  Re as UserPlan,
  H as ValidationError,
  je as VideoType,
  be as VoiceAccess,
  Ce as WsError,
  Et as createAgentManager,
  Mt as getAgent,
  It as mapVideoType
};
