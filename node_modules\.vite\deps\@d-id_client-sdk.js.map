{"version": 3, "sources": ["../../@d-id/client-sdk/dist/index.js"], "sourcesContent": ["var ye = Object.defineProperty;\nvar ve = (e, t, n) => t in e ? ye(e, t, { enumerable: !0, configurable: !0, writable: !0, value: n }) : e[t] = n;\nvar Y = (e, t, n) => (ve(e, typeof t != \"symbol\" ? t + \"\" : t, n), n);\nclass G extends Error {\n  constructor({\n    kind: n,\n    description: r,\n    error: s\n  }) {\n    super(JSON.stringify({\n      kind: n,\n      description: r\n    }));\n    Y(this, \"kind\");\n    Y(this, \"description\");\n    Y(this, \"error\");\n    this.kind = n, this.description = r, this.error = s;\n  }\n}\nclass ke extends G {\n  constructor(t, n) {\n    super({\n      kind: \"ChatCreationFailed\",\n      description: `Failed to create ${n ? \"persistent\" : \"\"} chat, mode: ${t}`\n    });\n  }\n}\nclass De extends G {\n  constructor(t) {\n    super({\n      kind: \"ChatModeDowngraded\",\n      description: `Chat mode downgraded to ${t}`\n    });\n  }\n}\nclass H extends G {\n  constructor(n, r) {\n    super({\n      kind: \"ValidationError\",\n      description: n\n    });\n    Y(this, \"key\");\n    this.key = r;\n  }\n}\nclass Ce extends G {\n  constructor(t) {\n    super({\n      kind: \"WSError\",\n      description: t\n    });\n  }\n}\nvar Re = /* @__PURE__ */ ((e) => (e.TRIAL = \"trial\", e.BASIC = \"basic\", e.ENTERPRISE = \"enterprise\", e.LITE = \"lite\", e.ADVANCED = \"advanced\", e))(Re || {}), Se = /* @__PURE__ */ ((e) => (e.TRIAL = \"deid-trial\", e.PRO = \"deid-pro\", e.ENTERPRISE = \"deid-enterprise\", e.LITE = \"deid-lite\", e.ADVANCED = \"deid-advanced\", e.BUILD = \"deid-api-build\", e.LAUNCH = \"deid-api-launch\", e.SCALE = \"deid-api-scale\", e))(Se || {}), Ie = /* @__PURE__ */ ((e) => (e.Created = \"created\", e.Started = \"started\", e.Done = \"done\", e.Error = \"error\", e.Rejected = \"rejected\", e.Ready = \"ready\", e))(Ie || {}), Ee = /* @__PURE__ */ ((e) => (e.Unrated = \"Unrated\", e.Positive = \"Positive\", e.Negative = \"Negative\", e))(Ee || {}), M = /* @__PURE__ */ ((e) => (e.Functional = \"Functional\", e.TextOnly = \"TextOnly\", e.Maintenance = \"Maintenance\", e.Playground = \"Playground\", e.DirectPlayback = \"DirectPlayback\", e.Off = \"Off\", e))(M || {}), q = /* @__PURE__ */ ((e) => (e.Embed = \"embed\", e.Query = \"query\", e.Partial = \"partial\", e.Answer = \"answer\", e.Complete = \"done\", e))(q || {}), Me = /* @__PURE__ */ ((e) => (e.KnowledgeProcessing = \"knowledge/processing\", e.KnowledgeIndexing = \"knowledge/indexing\", e.KnowledgeFailed = \"knowledge/error\", e.KnowledgeDone = \"knowledge/done\", e))(Me || {}), _e = /* @__PURE__ */ ((e) => (e.Knowledge = \"knowledge\", e.Document = \"document\", e.Record = \"record\", e))(_e || {}), Te = /* @__PURE__ */ ((e) => (e.Pdf = \"pdf\", e.Text = \"text\", e.Html = \"html\", e.Word = \"word\", e.Json = \"json\", e.Markdown = \"markdown\", e.Csv = \"csv\", e.Excel = \"excel\", e.Powerpoint = \"powerpoint\", e.Archive = \"archive\", e.Image = \"image\", e.Audio = \"audio\", e.Video = \"video\", e))(Te || {}), je = /* @__PURE__ */ ((e) => (e.Clip = \"clip\", e.Talk = \"talk\", e))(je || {});\nconst It = (e) => {\n  switch (e) {\n    case \"clip\":\n      return \"clip\";\n    case \"talk\":\n      return \"talk\";\n    default:\n      throw new Error(`Unknown video type: ${e}`);\n  }\n};\nvar h = /* @__PURE__ */ ((e) => (e.Start = \"START\", e.Stop = \"STOP\", e))(h || {}), Q = /* @__PURE__ */ ((e) => (e.Strong = \"STRONG\", e.Weak = \"WEAK\", e.Unknown = \"UNKNOWN\", e))(Q || {}), O = /* @__PURE__ */ ((e) => (e.Idle = \"IDLE\", e.Talking = \"TALKING\", e))(O || {}), A = /* @__PURE__ */ ((e) => (e.ChatAnswer = \"chat/answer\", e.ChatPartial = \"chat/partial\", e.StreamDone = \"stream/done\", e.StreamStarted = \"stream/started\", e.StreamFailed = \"stream/error\", e.StreamReady = \"stream/ready\", e.StreamCreated = \"stream/created\", e.StreamInterrupt = \"stream/interrupt\", e.StreamVideoCreated = \"stream-video/started\", e.StreamVideoDone = \"stream-video/done\", e.StreamVideoError = \"stream-video/error\", e.StreamVideoRejected = \"stream-video/rejected\", e))(A || {}), I = /* @__PURE__ */ ((e) => (e.New = \"new\", e.Fail = \"fail\", e.Connected = \"connected\", e.Connecting = \"connecting\", e.Closed = \"closed\", e.Completed = \"completed\", e.Disconnected = \"disconnected\", e))(I || {}), W = /* @__PURE__ */ ((e) => (e.Legacy = \"legacy\", e.Fluent = \"fluent\", e))(W || {}), Pe = /* @__PURE__ */ ((e) => (e.Amazon = \"amazon\", e.Microsoft = \"microsoft\", e.Afflorithmics = \"afflorithmics\", e.Elevenlabs = \"elevenlabs\", e))(Pe || {}), be = /* @__PURE__ */ ((e) => (e.Public = \"public\", e.Premium = \"premium\", e.Private = \"private\", e))(be || {});\nconst Ae = 45 * 1e3, Be = \"X-Playground-Chat\", Z = \"https://api.d-id.com\", Le = \"wss://notifications.d-id.com\", $e = \"79f81a83a67430be2bc0fd61042b8faa\", oe = (e) => new Promise((t) => setTimeout(t, e)), V = (e = 16) => {\n  const t = new Uint8Array(e);\n  return window.crypto.getRandomValues(t), Array.from(t, (n) => n.toString(16).padStart(2, \"0\")).join(\"\").slice(0, 13);\n}, ze = (e) => [M.TextOnly, M.Playground, M.Maintenance].includes(e), ce = (e) => e && [M.DirectPlayback, M.Off].includes(e);\nfunction Fe(e, t) {\n  let n;\n  return {\n    promise: new Promise((s, o) => {\n      n = setTimeout(() => o(new Error(t)), e);\n    }),\n    clear: () => clearTimeout(n)\n  };\n}\nasync function te(e, t) {\n  const n = {\n    limit: (t == null ? void 0 : t.limit) ?? 3,\n    delayMs: (t == null ? void 0 : t.delayMs) ?? 0,\n    timeout: (t == null ? void 0 : t.timeout) ?? 3e4,\n    timeoutErrorMessage: (t == null ? void 0 : t.timeoutErrorMessage) || \"Timeout error\",\n    shouldRetryFn: (t == null ? void 0 : t.shouldRetryFn) ?? (() => !0),\n    onRetry: (t == null ? void 0 : t.onRetry) ?? (() => {\n    })\n  };\n  let r;\n  for (let s = 1; s <= n.limit; s++)\n    try {\n      if (!n.timeout)\n        return await e();\n      const {\n        promise: o,\n        clear: c\n      } = Fe(n.timeout, n.timeoutErrorMessage), a = e().finally(c);\n      return await Promise.race([a, o]);\n    } catch (o) {\n      if (r = o, !n.shouldRetryFn(o) || s >= n.limit)\n        throw o;\n      await oe(n.delayMs), n.onRetry(o);\n    }\n  throw r;\n}\nfunction de() {\n  let e = window.localStorage.getItem(\"did_external_key_id\");\n  if (!e) {\n    let t = V();\n    window.localStorage.setItem(\"did_external_key_id\", t), e = t;\n  }\n  return e;\n}\nlet Ne = V();\nfunction le(e) {\n  if (e.type === \"bearer\")\n    return `Bearer ${e.token}`;\n  if (e.type === \"basic\")\n    return `Basic ${btoa(`${e.username}:${e.password}`)}`;\n  if (e.type === \"key\")\n    return `Client-Key ${e.clientKey}.${de()}_${Ne}`;\n  throw new Error(`Unknown auth type: ${e}`);\n}\nconst xe = (e) => te(e, {\n  limit: 3,\n  delayMs: 1e3,\n  timeout: 0,\n  shouldRetryFn: (t) => t.status === 429\n});\nfunction ue(e, t = Z, n) {\n  const r = async (s, o) => {\n    const {\n      skipErrorHandler: c,\n      ...a\n    } = o || {}, i = await xe(() => fetch(t + (s != null && s.startsWith(\"/\") ? s : `/${s}`), {\n      ...a,\n      headers: {\n        ...a.headers,\n        Authorization: le(e),\n        \"Content-Type\": \"application/json\"\n      }\n    }));\n    if (!i.ok) {\n      let d = await i.text().catch(() => `Failed to fetch with status ${i.status}`);\n      const l = new Error(d);\n      throw n && !c && n(l, {\n        url: s,\n        options: a,\n        headers: i.headers\n      }), l;\n    }\n    return i.json();\n  };\n  return {\n    get(s, o) {\n      return r(s, {\n        ...o,\n        method: \"GET\"\n      });\n    },\n    post(s, o, c) {\n      return r(s, {\n        ...c,\n        body: JSON.stringify(o),\n        method: \"POST\"\n      });\n    },\n    delete(s, o, c) {\n      return r(s, {\n        ...c,\n        body: JSON.stringify(o),\n        method: \"DELETE\"\n      });\n    },\n    patch(s, o, c) {\n      return r(s, {\n        ...c,\n        body: JSON.stringify(o),\n        method: \"PATCH\"\n      });\n    }\n  };\n}\nfunction fe(e, t = Z, n) {\n  const r = ue(e, `${t}/agents`, n);\n  return {\n    create(s, o) {\n      return r.post(\"/\", s, o);\n    },\n    getAgents(s, o) {\n      return r.get(`/${s ? `?tag=${s}` : \"\"}`, o).then((c) => c ?? []);\n    },\n    getById(s, o) {\n      return r.get(`/${s}`, o);\n    },\n    delete(s, o) {\n      return r.delete(`/${s}`, void 0, o);\n    },\n    update(s, o, c) {\n      return r.patch(`/${s}`, o, c);\n    },\n    newChat(s, o, c) {\n      return r.post(`/${s}/chat`, o, c);\n    },\n    chat(s, o, c, a) {\n      return r.post(`/${s}/chat/${o}`, c, a);\n    },\n    createRating(s, o, c, a) {\n      return r.post(`/${s}/chat/${o}/ratings`, c, a);\n    },\n    updateRating(s, o, c, a, i) {\n      return r.patch(`/${s}/chat/${o}/ratings/${c}`, a, i);\n    },\n    deleteRating(s, o, c, a) {\n      return r.delete(`/${s}/chat/${o}/ratings/${c}`, a);\n    },\n    getSTTToken(s, o) {\n      return r.get(`/${s}/stt-token`, o);\n    }\n  };\n}\nconst me = (e) => e.type === \"clip\" && e.presenter_id.startsWith(\"v2_\") ? \"clip_v2\" : e.type;\nfunction Je(e) {\n  var s, o, c, a;\n  const t = () => /Mobi|Android/i.test(navigator.userAgent) ? \"Mobile\" : \"Desktop\", n = () => {\n    const i = navigator.platform;\n    return i.toLowerCase().includes(\"win\") ? \"Windows\" : i.toLowerCase().includes(\"mac\") ? \"Mac OS X\" : i.toLowerCase().includes(\"linux\") ? \"Linux\" : \"Unknown\";\n  }, r = e.presenter;\n  return {\n    $os: `${n()}`,\n    isMobile: `${t() == \"Mobile\"}`,\n    browser: navigator.userAgent,\n    origin: window.location.origin,\n    agentType: me(r),\n    agentVoice: {\n      voiceId: (o = (s = e.presenter) == null ? void 0 : s.voice) == null ? void 0 : o.voice_id,\n      provider: (a = (c = e.presenter) == null ? void 0 : c.voice) == null ? void 0 : a.type\n    }\n  };\n}\nfunction We(e) {\n  var n, r, s, o, c, a;\n  const t = (n = e.llm) == null ? void 0 : n.prompt_customization;\n  return {\n    agentType: me(e.presenter),\n    owner_id: e.owner_id ?? \"\",\n    promptVersion: (r = e.llm) == null ? void 0 : r.prompt_version,\n    behavior: {\n      role: t == null ? void 0 : t.role,\n      personality: t == null ? void 0 : t.personality,\n      instructions: (s = e.llm) == null ? void 0 : s.instructions\n    },\n    temperature: (o = e.llm) == null ? void 0 : o.temperature,\n    knowledgeSource: t == null ? void 0 : t.knowledge_source,\n    starterQuestionsCount: (a = (c = e.knowledge) == null ? void 0 : c.starter_message) == null ? void 0 : a.length,\n    topicsToAvoid: t == null ? void 0 : t.topics_to_avoid,\n    maxResponseLength: t == null ? void 0 : t.max_response_length,\n    agentId: e.id,\n    access: e.access,\n    name: e.preview_name,\n    ...e.access === \"public\" ? {\n      from: \"agent-template\"\n    } : {}\n  };\n}\nconst Ue = (e) => e.reduce((t, n) => t + n, 0), ae = (e) => Ue(e) / e.length;\nfunction Ke(e, t, n) {\n  var i, d, l;\n  const {\n    event: r,\n    ...s\n  } = e, {\n    template: o\n  } = (t == null ? void 0 : t.llm) || {}, {\n    language: c\n  } = ((i = t == null ? void 0 : t.presenter) == null ? void 0 : i.voice) || {};\n  return {\n    ...s,\n    llm: {\n      ...s.llm,\n      template: o\n    },\n    script: {\n      ...s.script,\n      provider: {\n        ...(d = s == null ? void 0 : s.script) == null ? void 0 : d.provider,\n        language: c\n      }\n    },\n    stitch: (t == null ? void 0 : t.presenter.type) === \"talk\" ? (l = t == null ? void 0 : t.presenter) == null ? void 0 : l.stitch : void 0,\n    ...n\n  };\n}\nlet ee = {};\nconst He = \"https://api-js.mixpanel.com/track/?verbose=1&ip=1\";\nfunction qe(e) {\n  const t = window != null && window.hasOwnProperty(\"DID_AGENTS_API\") ? \"agents-ui\" : \"agents-sdk\";\n  return {\n    token: e.token || \"testKey\",\n    distinct_id: e.distinctId || de(),\n    agentId: e.agentId,\n    additionalProperties: {},\n    isEnabled: e.isEnabled ?? !0,\n    getRandom: V,\n    enrich(n) {\n      this.additionalProperties = {\n        ...this.additionalProperties,\n        ...n\n      };\n    },\n    async track(n, r) {\n      if (!this.isEnabled)\n        return Promise.resolve();\n      const {\n        audioPath: s,\n        ...o\n      } = r || {}, c = {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/x-www-form-urlencoded\"\n        },\n        body: new URLSearchParams({\n          data: JSON.stringify([{\n            event: n,\n            properties: {\n              ...this.additionalProperties,\n              ...o,\n              agentId: this.agentId,\n              source: t,\n              token: this.token,\n              time: Date.now(),\n              $insert_id: this.getRandom(),\n              origin: window.location.href,\n              \"Screen Height\": window.screen.height || window.innerWidth,\n              \"Screen Width\": window.screen.width || window.innerHeight,\n              \"User Agent\": navigator.userAgent\n            }\n          }])\n        })\n      };\n      try {\n        return await fetch(He, c).then((a) => a.json());\n      } catch (a) {\n        return console.error(a);\n      }\n    },\n    linkTrack(n, r, s, o) {\n      ee[n] || (ee[n] = {\n        events: {},\n        resolvedDependencies: []\n      }), o.includes(s) || o.push(s);\n      const c = ee[n];\n      if (c.events[s] = {\n        props: r\n      }, c.resolvedDependencies.push(s), o.every((i) => c.resolvedDependencies.includes(i))) {\n        const i = o.reduce((d, l) => c.events[l] ? {\n          ...d,\n          ...c.events[l].props\n        } : d, {});\n        this.track(n, i), c.resolvedDependencies = c.resolvedDependencies.filter((d) => !o.includes(d)), o.forEach((d) => {\n          delete c.events[d];\n        });\n      }\n    }\n  };\n}\nfunction ge() {\n  let e = 0;\n  return {\n    reset: () => e = 0,\n    update: () => e = Date.now(),\n    get: (t = !1) => t ? Date.now() - e : e\n  };\n}\nconst N = ge(), re = ge();\nfunction he(e) {\n  return e === M.Playground ? {\n    headers: {\n      [Be]: \"true\"\n    }\n  } : {};\n}\nasync function we(e, t, n, r, s = !1, o) {\n  try {\n    return !o && !ce(r) && (o = await t.newChat(e.id, {\n      persist: s\n    }, he(r)), n.track(\"agent-chat\", {\n      event: \"created\",\n      chatId: o.id,\n      mode: r\n    })), {\n      chat: o,\n      chatMode: (o == null ? void 0 : o.chat_mode) ?? r\n    };\n  } catch (c) {\n    try {\n      const a = JSON.parse(c.message);\n      if ((a == null ? void 0 : a.kind) === \"InsufficientCreditsError\")\n        throw new Error(\"InsufficientCreditsError\");\n    } catch (a) {\n      console.error(\"Error parsing the error message:\", a);\n    }\n    throw new Error(\"Cannot create new chat\");\n  }\n}\nfunction Ve(e) {\n  return e && e.length > 0 ? e : [];\n}\nfunction Oe(e, t, n) {\n  if (!e)\n    throw new Error(\"Please connect to the agent first\");\n  if (!e.interruptAvailable)\n    throw new Error(\"Interrupt is not enabled for this stream\");\n  if (t !== W.Fluent)\n    throw new Error(\"Interrupt only available for Fluent streams\");\n  if (!n)\n    throw new Error(\"No active video to interrupt\");\n}\nasync function Xe(e, t) {\n  const n = {\n    type: A.StreamInterrupt,\n    videoId: t,\n    timestamp: Date.now()\n  };\n  e.sendDataChannelMessage(JSON.stringify(n));\n}\nfunction Ye(e) {\n  return new Promise((t, n) => {\n    const {\n      callbacks: r,\n      host: s,\n      auth: o\n    } = e, {\n      onMessage: c = null,\n      onOpen: a = null,\n      onClose: i = null,\n      onError: d = null\n    } = r || {}, l = new WebSocket(`${s}?authorization=${le(o)}`);\n    l.onmessage = c, l.onclose = i, l.onerror = (y) => {\n      console.error(y), d == null || d(\"Websocket failed to connect\", y), n(y);\n    }, l.onopen = (y) => {\n      a == null || a(y), t(l);\n    };\n  });\n}\nasync function Qe(e) {\n  const {\n    retries: t = 1\n  } = e;\n  let n = null;\n  for (let r = 0; (n == null ? void 0 : n.readyState) !== WebSocket.OPEN; r++)\n    try {\n      n = await Ye(e);\n    } catch (s) {\n      if (r === t)\n        throw s;\n      await oe(r * 500);\n    }\n  return n;\n}\nasync function Ze(e, t, n) {\n  const r = n != null && n.onMessage ? [n.onMessage] : [], s = await Qe({\n    auth: e,\n    host: t,\n    callbacks: {\n      onError: (o) => {\n        var c;\n        return (c = n.onError) == null ? void 0 : c.call(n, new Ce(o));\n      },\n      onMessage(o) {\n        const c = JSON.parse(o.data);\n        r.forEach((a) => a(c.event, c));\n      }\n    }\n  });\n  return {\n    socket: s,\n    disconnect: () => s.close(),\n    subscribeToEvents: (o) => r.push(o)\n  };\n}\nfunction Ge(e) {\n  if (e.answer !== void 0)\n    return e.answer;\n  let t = 0, n = \"\";\n  for (; t in e; )\n    n += e[t++];\n  return n;\n}\nfunction et(e, t, n, r, s) {\n  const o = r.messages[r.messages.length - 1];\n  if (!(e === q.Partial || e === q.Answer) || (o == null ? void 0 : o.role) !== \"assistant\")\n    return;\n  const {\n    content: c,\n    sequence: a\n  } = t;\n  e === q.Partial ? n[a] = c : n.answer = c;\n  const i = Ge(n);\n  (o.content !== i || e === q.Answer) && (o.content = i, s == null || s([...r.messages], e));\n}\nfunction tt(e, t, n, r, s) {\n  let o = {};\n  return {\n    clearQueue: () => o = {},\n    onMessage: (c, a) => {\n      var i, d;\n      if (\"content\" in a)\n        et(c, a, o, t, n.callbacks.onNewMessage), c === q.Answer && e.track(\"agent-message-received\", {\n          messages: t.messages.length,\n          mode: t.chatMode\n        });\n      else {\n        const l = A, y = [l.StreamVideoDone, l.StreamVideoError, l.StreamVideoRejected], _ = [l.StreamFailed, l.StreamVideoError, l.StreamVideoRejected], T = Ke(a, r, {\n          mode: t.chatMode\n        });\n        if (c = c, c === l.StreamVideoCreated)\n          e.linkTrack(\"agent-video\", T, l.StreamVideoCreated, [\"start\"]);\n        else if (y.includes(c)) {\n          const j = c.split(\"/\")[1];\n          _.includes(c) ? e.track(\"agent-video\", {\n            ...T,\n            event: j\n          }) : e.linkTrack(\"agent-video\", {\n            ...T,\n            event: j\n          }, c, [\"done\"]);\n        }\n        _.includes(c) && ((d = (i = n.callbacks).onError) == null || d.call(i, new Error(`Stream failed with event ${c}`), {\n          data: a\n        })), a.event === l.StreamDone && s();\n      }\n    }\n  };\n}\nfunction rt(e, t, n, r) {\n  const s = ue(e, `${t}/agents/${n}`, r);\n  return {\n    createStream(o) {\n      return s.post(\"/streams\", o);\n    },\n    startConnection(o, c, a) {\n      return s.post(`/streams/${o}/sdp`, {\n        session_id: a,\n        answer: c\n      });\n    },\n    addIceCandidate(o, c, a) {\n      return s.post(`/streams/${o}/ice`, {\n        session_id: a,\n        ...c\n      });\n    },\n    sendStreamRequest(o, c, a) {\n      return s.post(`/streams/${o}`, {\n        session_id: c,\n        ...a\n      });\n    },\n    close(o, c) {\n      return s.delete(`/streams/${o}`, {\n        session_id: c\n      });\n    }\n  };\n}\nfunction nt(e, t, n) {\n  const r = (t.timestamp - e.timestamp) / 1e3;\n  return {\n    duration: r,\n    bytesReceived: t.bytesReceived - e.bytesReceived,\n    bitrate: Math.round((t.bytesReceived - e.bytesReceived) * 8 / r),\n    packetsReceived: t.packetsReceived - e.packetsReceived,\n    packetsLost: t.packetsLost - e.packetsLost,\n    framesDropped: t.framesDropped - e.framesDropped,\n    framesDecoded: t.framesDecoded - e.framesDecoded,\n    jitter: t.jitter,\n    avgJitterDelayInInterval: (t.jitterBufferDelay - e.jitterBufferDelay) / (t.jitterBufferEmittedCount - e.jitterBufferEmittedCount),\n    jitterBufferEmittedCount: t.jitterBufferEmittedCount - e.jitterBufferEmittedCount,\n    jitterBufferDelay: (t.jitterBufferDelay - e.jitterBufferDelay) / r,\n    framesPerSecond: t.framesPerSecond,\n    freezeCount: t.freezeCount - e.freezeCount,\n    freezeDuration: t.freezeDuration - e.freezeDuration,\n    lowFpsCount: n\n  };\n}\nfunction at(e) {\n  return e.filter((t) => t.freezeCount > 0 || t.framesPerSecond < 21 || t.framesDropped > 0 || t.packetsLost > 0).map((t) => {\n    const {\n      timestamp: n,\n      ...r\n    } = t, s = [];\n    return t.freezeCount > 0 && s.push(\"freeze\"), t.framesPerSecond < 21 && s.push(\"low fps\"), t.framesDropped > 0 && s.push(\"frames dropped\"), t.packetsLost > 0 && s.push(\"packet loss\"), {\n      ...r,\n      causes: s\n    };\n  });\n}\nfunction it(e) {\n  let t = \"\", n = 0;\n  for (const r of e.values())\n    if (r && r.type === \"codec\" && r.mimeType.startsWith(\"video\") && (t = r.mimeType.split(\"/\")[1]), r && r.type === \"candidate-pair\" && (n = r.currentRoundTripTime), r && r.type === \"inbound-rtp\" && r.kind === \"video\")\n      return {\n        codec: t,\n        rtt: n,\n        timestamp: r.timestamp,\n        bytesReceived: r.bytesReceived,\n        packetsReceived: r.packetsReceived,\n        packetsLost: r.packetsLost,\n        framesDropped: r.framesDropped,\n        framesDecoded: r.framesDecoded,\n        jitter: r.jitter,\n        jitterBufferDelay: r.jitterBufferDelay,\n        jitterBufferEmittedCount: r.jitterBufferEmittedCount,\n        avgJitterDelayInInterval: r.jitterBufferDelay / r.jitterBufferEmittedCount,\n        frameWidth: r.frameWidth,\n        frameHeight: r.frameHeight,\n        framesPerSecond: r.framesPerSecond,\n        freezeCount: r.freezeCount,\n        freezeDuration: r.totalFreezesDuration\n      };\n  return {};\n}\nfunction st(e, t, n) {\n  const r = e.map((i, d) => d === 0 ? n ? {\n    timestamp: i.timestamp,\n    duration: 0,\n    rtt: i.rtt,\n    bytesReceived: i.bytesReceived - n.bytesReceived,\n    bitrate: (i.bytesReceived - n.bytesReceived) * 8 / (t / 1e3),\n    packetsReceived: i.packetsReceived - n.packetsReceived,\n    packetsLost: i.packetsLost - n.packetsLost,\n    framesDropped: i.framesDropped - n.framesDropped,\n    framesDecoded: i.framesDecoded - n.framesDecoded,\n    jitter: i.jitter,\n    jitterBufferDelay: i.jitterBufferDelay - n.jitterBufferDelay,\n    jitterBufferEmittedCount: i.jitterBufferEmittedCount - n.jitterBufferEmittedCount,\n    avgJitterDelayInInterval: (i.jitterBufferDelay - n.jitterBufferDelay) / (i.jitterBufferEmittedCount - n.jitterBufferEmittedCount),\n    framesPerSecond: i.framesPerSecond,\n    freezeCount: i.freezeCount - n.freezeCount,\n    freezeDuration: i.freezeDuration - n.freezeDuration\n  } : {\n    timestamp: i.timestamp,\n    rtt: i.rtt,\n    duration: 0,\n    bytesReceived: i.bytesReceived,\n    bitrate: i.bytesReceived * 8 / (t / 1e3),\n    packetsReceived: i.packetsReceived,\n    packetsLost: i.packetsLost,\n    framesDropped: i.framesDropped,\n    framesDecoded: i.framesDecoded,\n    jitter: i.jitter,\n    jitterBufferDelay: i.jitterBufferDelay,\n    jitterBufferEmittedCount: i.jitterBufferEmittedCount,\n    avgJitterDelayInInterval: i.jitterBufferDelay / i.jitterBufferEmittedCount,\n    framesPerSecond: i.framesPerSecond,\n    freezeCount: i.freezeCount,\n    freezeDuration: i.freezeDuration\n  } : {\n    timestamp: i.timestamp,\n    duration: t * d / 1e3,\n    rtt: i.rtt,\n    bytesReceived: i.bytesReceived - e[d - 1].bytesReceived,\n    bitrate: (i.bytesReceived - e[d - 1].bytesReceived) * 8 / (t / 1e3),\n    packetsReceived: i.packetsReceived - e[d - 1].packetsReceived,\n    packetsLost: i.packetsLost - e[d - 1].packetsLost,\n    framesDropped: i.framesDropped - e[d - 1].framesDropped,\n    framesDecoded: i.framesDecoded - e[d - 1].framesDecoded,\n    jitter: i.jitter,\n    jitterBufferDelay: i.jitterBufferDelay - e[d - 1].jitterBufferDelay,\n    jitterBufferEmittedCount: i.jitterBufferEmittedCount - e[d - 1].jitterBufferEmittedCount,\n    avgJitterDelayInInterval: (i.jitterBufferDelay - e[d - 1].jitterBufferDelay) / (i.jitterBufferEmittedCount - e[d - 1].jitterBufferEmittedCount),\n    framesPerSecond: i.framesPerSecond,\n    freezeCount: i.freezeCount - e[d - 1].freezeCount,\n    freezeDuration: i.freezeDuration - e[d - 1].freezeDuration\n  }), s = at(r), o = s.reduce((i, d) => i + (d.causes.includes(\"low fps\") ? 1 : 0), 0), c = r.filter((i) => !!i.avgJitterDelayInInterval).map((i) => i.avgJitterDelayInInterval), a = r.filter((i) => !!i.rtt).map((i) => i.rtt);\n  return {\n    webRTCStats: {\n      anomalies: s,\n      minRtt: Math.min(...a),\n      avgRtt: ae(a),\n      maxRtt: Math.max(...a),\n      aggregateReport: nt(e[0], e[e.length - 1], o),\n      minJitterDelayInInterval: Math.min(...c),\n      maxJitterDelayInInterval: Math.max(...c),\n      avgJitterDelayInInterval: ae(c)\n    },\n    codec: e[0].codec,\n    resolution: `${e[0].frameWidth}x${e[0].frameHeight}`\n  };\n}\nconst ne = 100, ot = Math.max(Math.ceil(400 / ne), 1), ct = 0.25, dt = 0.28;\nfunction lt() {\n  let e = 0, t, n, r = 0;\n  return (s) => {\n    for (const o of s.values())\n      if (o && o.type === \"inbound-rtp\" && o.kind === \"video\") {\n        const c = o.jitterBufferDelay, a = o.jitterBufferEmittedCount;\n        if (n && a > n) {\n          const l = c - t, y = a - n;\n          r = l / y;\n        }\n        t = c, n = a;\n        const i = o.framesDecoded, d = i - e > 0;\n        return e = i, {\n          isReceiving: d,\n          avgJitterDelayInInterval: r,\n          freezeCount: o.freezeCount\n        };\n      }\n    return {\n      isReceiving: !1,\n      avgJitterDelayInInterval: r\n    };\n  };\n}\nfunction ut(e, t, n, r, s, o = !1) {\n  let c = [], a, i = 0, d = !1, l = Q.Unknown, y = Q.Unknown, _ = 0, T = 0;\n  const j = lt();\n  return setInterval(async () => {\n    const x = await e.getStats(), {\n      isReceiving: R,\n      avgJitterDelayInInterval: U,\n      freezeCount: K\n    } = j(x), P = it(x);\n    if (R)\n      i = 0, _ = K - T, y = U < ct ? Q.Strong : U > dt && _ > 1 ? Q.Weak : l, y !== l && (s == null || s(y), l = y, T += _, _ = 0), d || (r == null || r(h.Start), a = c[c.length - 1], c = [], d = !0), c.push(P);\n    else if (d && (i++, i >= ot)) {\n      const u = st(c, ne, a);\n      r == null || r(h.Stop, u), t() || n(), T = K, d = !1;\n    }\n  }, ne);\n}\nlet pe = !1;\nconst B = (e, t) => pe && console.log(e, t), ft = (window.RTCPeerConnection || window.webkitRTCPeerConnection || window.mozRTCPeerConnection).bind(window);\nfunction ie(e) {\n  switch (e) {\n    case \"connected\":\n      return I.Connected;\n    case \"checking\":\n      return I.Connecting;\n    case \"failed\":\n      return I.Fail;\n    case \"new\":\n      return I.New;\n    case \"closed\":\n      return I.Closed;\n    case \"disconnected\":\n      return I.Disconnected;\n    case \"completed\":\n      return I.Completed;\n    default:\n      return I.New;\n  }\n}\nfunction mt(e) {\n  const [t, n = \"\"] = e.split(/:(.+)/);\n  try {\n    const r = JSON.parse(n);\n    return B(\"parsed data channel message\", {\n      subject: t,\n      data: r\n    }), {\n      subject: t,\n      data: r\n    };\n  } catch (r) {\n    return B(\"Failed to parse data channel message, returning data as string\", {\n      subject: t,\n      rawData: n,\n      error: r\n    }), {\n      subject: t,\n      data: n\n    };\n  }\n}\nfunction gt({\n  statsSignal: e,\n  dataChannelSignal: t,\n  onVideoStateChange: n,\n  report: r\n}) {\n  e === h.Start && t === h.Start ? n == null || n(h.Start) : e === h.Stop && t === h.Stop && (n == null || n(h.Stop, r));\n}\nfunction ht({\n  statsSignal: e,\n  dataChannelSignal: t,\n  onVideoStateChange: n,\n  onAgentActivityStateChange: r,\n  report: s\n}) {\n  e === h.Start ? n == null || n(h.Start) : e === h.Stop && (n == null || n(h.Stop, s)), t === h.Start ? r == null || r(O.Talking) : t === h.Stop && (r == null || r(O.Idle));\n}\nfunction se({\n  statsSignal: e,\n  dataChannelSignal: t,\n  onVideoStateChange: n,\n  onAgentActivityStateChange: r,\n  streamType: s,\n  report: o\n}) {\n  s === W.Legacy ? gt({\n    statsSignal: e,\n    dataChannelSignal: t,\n    onVideoStateChange: n,\n    report: o\n  }) : s === W.Fluent && ht({\n    statsSignal: e,\n    dataChannelSignal: t,\n    onVideoStateChange: n,\n    onAgentActivityStateChange: r,\n    report: o\n  });\n}\nasync function wt(e, t, {\n  debug: n = !1,\n  callbacks: r,\n  auth: s,\n  baseURL: o = Z,\n  analytics: c\n}) {\n  var J;\n  pe = n;\n  let a = !1, i = !1, d = h.Stop, l = h.Stop;\n  const {\n    startConnection: y,\n    sendStreamRequest: _,\n    close: T,\n    createStream: j,\n    addIceCandidate: x\n  } = rt(s, o, e, r.onError), {\n    id: R,\n    offer: U,\n    ice_servers: K,\n    session_id: P,\n    fluent: u,\n    interrupt_enabled: v\n  } = await j(t);\n  (J = r.onStreamCreated) == null || J.call(r, {\n    stream_id: R,\n    session_id: P,\n    agent_id: e\n  });\n  const m = new ft({\n    iceServers: K\n  }), D = m.createDataChannel(\"JanusDataChannel\");\n  if (!P)\n    throw new Error(\"Could not create session_id\");\n  const w = u ? W.Fluent : W.Legacy;\n  c.enrich({\n    \"stream-type\": w\n  });\n  const p = t.stream_warmup && !u, b = () => a, L = () => {\n    var f;\n    a = !0, i && ((f = r.onConnectionStateChange) == null || f.call(r, I.Connected));\n  }, $ = ut(m, b, L, (f, g) => se({\n    statsSignal: l = f,\n    dataChannelSignal: w === W.Legacy ? d : void 0,\n    onVideoStateChange: r.onVideoStateChange,\n    onAgentActivityStateChange: r.onAgentActivityStateChange,\n    report: g,\n    streamType: w\n  }), (f) => {\n    var g;\n    return (g = r.onConnectivityStateChange) == null ? void 0 : g.call(r, f);\n  }, p);\n  m.onicecandidate = (f) => {\n    var g;\n    B(\"peerConnection.onicecandidate\", f);\n    try {\n      f.candidate && f.candidate.sdpMid && f.candidate.sdpMLineIndex !== null ? x(R, {\n        candidate: f.candidate.candidate,\n        sdpMid: f.candidate.sdpMid,\n        sdpMLineIndex: f.candidate.sdpMLineIndex\n      }, P) : x(R, {\n        candidate: null\n      }, P);\n    } catch (S) {\n      (g = r.onError) == null || g.call(r, S, {\n        streamId: R\n      });\n    }\n  }, D.onopen = () => {\n    i = !0, (!p || a) && L();\n  };\n  const k = (f) => {\n    var g;\n    (g = r.onVideoIdChange) == null || g.call(r, f);\n  };\n  function E(f, g) {\n    if (f === A.StreamStarted && typeof g == \"object\" && \"metadata\" in g) {\n      const S = g.metadata;\n      k(S.videoId);\n    }\n    f === A.StreamDone && k(null), d = f === A.StreamStarted ? h.Start : h.Stop, se({\n      statsSignal: w === W.Legacy ? l : void 0,\n      dataChannelSignal: d,\n      onVideoStateChange: r.onVideoStateChange,\n      onAgentActivityStateChange: r.onAgentActivityStateChange,\n      streamType: w\n    });\n  }\n  function C(f, g) {\n    const S = typeof g == \"string\" ? g : g == null ? void 0 : g.metadata;\n    S && c.enrich({\n      streamMetadata: S\n    }), c.track(\"agent-chat\", {\n      event: \"ready\"\n    });\n  }\n  const z = {\n    [A.StreamStarted]: E,\n    [A.StreamDone]: E,\n    [A.StreamReady]: C\n  };\n  D.onmessage = (f) => {\n    var X;\n    const {\n      subject: g,\n      data: S\n    } = mt(f.data);\n    (X = z[g]) == null || X.call(z, g, S);\n  }, m.oniceconnectionstatechange = () => {\n    var g;\n    B(\"peerConnection.oniceconnectionstatechange => \" + m.iceConnectionState);\n    const f = ie(m.iceConnectionState);\n    f !== I.Connected && ((g = r.onConnectionStateChange) == null || g.call(r, f));\n  }, m.ontrack = (f) => {\n    var g;\n    B(\"peerConnection.ontrack\", f), (g = r.onSrcObjectReady) == null || g.call(r, f.streams[0]);\n  }, await m.setRemoteDescription(U), B(\"set remote description OK\");\n  const F = await m.createAnswer();\n  return B(\"create answer OK\"), await m.setLocalDescription(F), B(\"set local description OK\"), await y(R, F, P), B(\"start connection OK\"), {\n    /**\n     * Method to send request to server to get clip or talk depend on you payload\n     * @param payload\n     */\n    speak(f) {\n      return _(R, P, f);\n    },\n    /**\n     * Method to close RTC connection\n     */\n    async disconnect() {\n      var f;\n      if (R) {\n        const g = ie(m.iceConnectionState);\n        if (m) {\n          if (g === I.New) {\n            clearInterval($);\n            return;\n          }\n          m.close(), m.oniceconnectionstatechange = null, m.onnegotiationneeded = null, m.onicecandidate = null, m.ontrack = null;\n        }\n        try {\n          g === I.Connected && await T(R, P).catch((S) => {\n          });\n        } catch (S) {\n          B(\"Error on close stream connection\", S);\n        }\n        (f = r.onAgentActivityStateChange) == null || f.call(r, O.Idle), clearInterval($);\n      }\n    },\n    /**\n     * Method to send data channel messages to the server\n     */\n    sendDataChannelMessage(f) {\n      var g, S;\n      if (!a || D.readyState !== \"open\") {\n        B(\"Data channel is not ready for sending messages\"), (g = r.onError) == null || g.call(r, new Error(\"Data channel is not ready for sending messages\"), {\n          streamId: R\n        });\n        return;\n      }\n      try {\n        D.send(f);\n      } catch (X) {\n        B(\"Error sending data channel message\", X), (S = r.onError) == null || S.call(r, X, {\n          streamId: R\n        });\n      }\n    },\n    /**\n     * Session identifier information, should be returned in the body of all streaming requests\n     */\n    sessionId: P,\n    /**\n     * Id of current RTC stream\n     */\n    streamId: R,\n    streamType: w,\n    interruptAvailable: v\n  };\n}\nfunction pt(e) {\n  const {\n    streamOptions: t\n  } = e ?? {};\n  return {\n    output_resolution: t == null ? void 0 : t.outputResolution,\n    session_timeout: t == null ? void 0 : t.sessionTimeout,\n    stream_warmup: t == null ? void 0 : t.streamWarmup,\n    compatibility_mode: t == null ? void 0 : t.compatibilityMode,\n    fluent: t == null ? void 0 : t.fluent\n  };\n}\nfunction yt(e, t, n, r, s) {\n  s === W.Fluent ? vt(e, t, n, r, s) : Dt(e, t, n, r, s);\n}\nfunction vt(e, t, n, r, s) {\n  e === h.Start ? r.track(\"stream-session\", {\n    event: \"start\",\n    \"stream-type\": s\n  }) : e === h.Stop && r.track(\"stream-session\", {\n    event: \"stop\",\n    is_greenscreen: t.presenter.type === \"clip\" && t.presenter.is_greenscreen,\n    background: t.presenter.type === \"clip\" && t.presenter.background,\n    \"stream-type\": s,\n    ...n\n  });\n}\nfunction kt(e, t, n, r) {\n  N.get() <= 0 || (e === h.Start ? n.linkTrack(\"agent-video\", {\n    event: \"start\",\n    latency: N.get(!0),\n    \"stream-type\": r\n  }, \"start\", [A.StreamVideoCreated]) : e === h.Stop && n.linkTrack(\"agent-video\", {\n    event: \"stop\",\n    is_greenscreen: t.presenter.type === \"clip\" && t.presenter.is_greenscreen,\n    background: t.presenter.type === \"clip\" && t.presenter.background,\n    \"stream-type\": r\n  }, \"done\", [A.StreamVideoDone]));\n}\nfunction Dt(e, t, n, r, s) {\n  N.get() <= 0 || (e === h.Start ? r.linkTrack(\"agent-video\", {\n    event: \"start\",\n    latency: N.get(!0),\n    \"stream-type\": s\n  }, \"start\", [A.StreamVideoCreated]) : e === h.Stop && r.linkTrack(\"agent-video\", {\n    event: \"stop\",\n    is_greenscreen: t.presenter.type === \"clip\" && t.presenter.is_greenscreen,\n    background: t.presenter.type === \"clip\" && t.presenter.background,\n    \"stream-type\": s,\n    ...n\n  }, \"done\", [A.StreamVideoDone]));\n}\nfunction Ct(e, t, n) {\n  return N.reset(), new Promise(async (r, s) => {\n    try {\n      const o = await wt(e.id, pt(t), {\n        ...t,\n        analytics: n,\n        callbacks: {\n          ...t.callbacks,\n          onConnectionStateChange: (c) => {\n            var a, i;\n            (i = (a = t.callbacks).onConnectionStateChange) == null || i.call(a, c), c === I.Connected && r(o);\n          },\n          onVideoStateChange: (c, a) => {\n            var i, d;\n            (d = (i = t.callbacks).onVideoStateChange) == null || d.call(i, c), yt(c, e, a, n, o.streamType);\n          },\n          onAgentActivityStateChange: (c) => {\n            var a, i;\n            (i = (a = t.callbacks).onAgentActivityStateChange) == null || i.call(a, c), c === O.Talking ? re.update() : re.reset(), kt(c === O.Talking ? h.Start : h.Stop, e, n, o.streamType);\n          }\n        }\n      });\n    } catch (o) {\n      s(o);\n    }\n  });\n}\nasync function Rt(e, t, n, r, s) {\n  var y, _, T, j;\n  const o = we(e, n, r, t.mode, t.persistentChat, s), c = Ct(e, t, r), [a, i] = await Promise.all([o, c]), {\n    chat: d,\n    chatMode: l\n  } = a;\n  return l && l !== t.mode && (t.mode = l, (_ = (y = t.callbacks).onModeChange) == null || _.call(y, l), l !== M.Functional) ? ((j = (T = t.callbacks).onError) == null || j.call(T, new De(l)), i == null || i.disconnect(), {\n    chat: d\n  }) : {\n    chat: d,\n    streamingManager: i\n  };\n}\nasync function Et(e, t) {\n  var U, K, P;\n  let n = !0, r = null;\n  const s = t.mixpanelKey || $e, o = t.wsURL || Le, c = t.baseURL || Z, a = {\n    messages: [],\n    chatMode: t.mode || M.Functional\n  }, i = qe({\n    token: s,\n    agentId: e,\n    isEnabled: t.enableAnalitics,\n    distinctId: t.distinctId\n  });\n  i.track(\"agent-sdk\", {\n    event: \"init\"\n  });\n  const d = fe(t.auth, c, t.callbacks.onError), l = await d.getById(e);\n  i.enrich(We(l));\n  const {\n    onMessage: y,\n    clearQueue: _\n  } = tt(i, a, t, l, () => {\n    var u;\n    return (u = a.socketManager) == null ? void 0 : u.disconnect();\n  });\n  a.messages = Ve(t.initialMessages), (K = (U = t.callbacks).onNewMessage) == null || K.call(U, [...a.messages], \"answer\");\n  const T = (u) => {\n    r = u;\n  };\n  i.track(\"agent-sdk\", {\n    event: \"loaded\",\n    ...Je(l)\n  });\n  async function j(u) {\n    var b, L, $, k, E, C, z;\n    (L = (b = t.callbacks).onConnectionStateChange) == null || L.call(b, I.Connecting), N.reset(), u && !n && (delete a.chat, (k = ($ = t.callbacks).onNewMessage) == null || k.call($, [...a.messages], \"answer\"));\n    const v = t.mode === M.DirectPlayback ? Promise.resolve(void 0) : Ze(t.auth, o, {\n      onMessage: y,\n      onError: t.callbacks.onError\n    }), m = te(() => Rt(l, {\n      ...t,\n      callbacks: {\n        ...t.callbacks,\n        onVideoIdChange: T\n      }\n    }, d, i, a.chat), {\n      limit: 3,\n      timeout: Ae,\n      timeoutErrorMessage: \"Timeout initializing the stream\",\n      // Retry on all errors except for connection errors and rate limit errors, these are already handled in client level.\n      shouldRetryFn: (F) => (F == null ? void 0 : F.message) !== \"Could not connect\" && F.status !== 429,\n      delayMs: 1e3\n    }).catch((F) => {\n      var J, f;\n      throw R(M.Maintenance), (f = (J = t.callbacks).onConnectionStateChange) == null || f.call(J, I.Fail), F;\n    }), [D, {\n      streamingManager: w,\n      chat: p\n    }] = await Promise.all([v, m]);\n    p && p.id !== ((E = a.chat) == null ? void 0 : E.id) && ((z = (C = t.callbacks).onNewChat) == null || z.call(C, p.id)), a.streamingManager = w, a.socketManager = D, a.chat = p, n = !1, i.enrich({\n      chatId: p == null ? void 0 : p.id,\n      streamId: w == null ? void 0 : w.streamId,\n      mode: a.chatMode\n    }), R((p == null ? void 0 : p.chat_mode) ?? t.mode ?? M.Functional);\n  }\n  async function x() {\n    var u, v, m, D;\n    (u = a.socketManager) == null || u.disconnect(), await ((v = a.streamingManager) == null ? void 0 : v.disconnect()), delete a.streamingManager, delete a.socketManager, (D = (m = t.callbacks).onConnectionStateChange) == null || D.call(m, I.Disconnected);\n  }\n  async function R(u) {\n    var v, m;\n    u !== a.chatMode && (i.track(\"agent-mode-change\", {\n      mode: u\n    }), a.chatMode = u, a.chatMode !== M.Functional && await x(), (m = (v = t.callbacks).onModeChange) == null || m.call(v, u));\n  }\n  return {\n    agent: l,\n    getStreamType: () => {\n      var u;\n      return (u = a.streamingManager) == null ? void 0 : u.streamType;\n    },\n    getIsInterruptAvailable: () => {\n      var u;\n      return ((u = a.streamingManager) == null ? void 0 : u.interruptAvailable) ?? !1;\n    },\n    starterMessages: ((P = l.knowledge) == null ? void 0 : P.starter_message) || [],\n    getSTTToken: () => d.getSTTToken(l.id),\n    changeMode: R,\n    enrichAnalytics: i.enrich,\n    async connect() {\n      await j(!0), i.track(\"agent-chat\", {\n        event: \"connect\",\n        mode: a.chatMode\n      });\n    },\n    async reconnect() {\n      await x(), await j(!1), i.track(\"agent-chat\", {\n        event: \"reconnect\",\n        mode: a.chatMode\n      });\n    },\n    async disconnect() {\n      await x(), i.track(\"agent-chat\", {\n        event: \"disconnect\",\n        mode: a.chatMode\n      });\n    },\n    async chat(u) {\n      var w, p, b, L, $;\n      const v = () => {\n        if (ce(t.mode))\n          throw new H(`${t.mode} is enabled, chat is disabled`);\n        if (u.length >= 800)\n          throw new H(\"Message cannot be more than 800 characters\");\n        if (u.length === 0)\n          throw new H(\"Message cannot be empty\");\n        if (a.chatMode === M.Maintenance)\n          throw new H(\"Chat is in maintenance mode\");\n        if (![M.TextOnly, M.Playground].includes(a.chatMode)) {\n          if (!a.streamingManager)\n            throw new H(\"Streaming manager is not initialized\");\n          if (!a.chat)\n            throw new H(\"Chat is not initialized\");\n        }\n      }, m = async () => {\n        var k, E;\n        if (!a.chat) {\n          const C = await we(l, d, i, a.chatMode, t.persistentChat);\n          if (!C.chat)\n            throw new ke(a.chatMode, !!t.persistentChat);\n          a.chat = C.chat, (E = (k = t.callbacks).onNewChat) == null || E.call(k, a.chat.id);\n        }\n        return a.chat.id;\n      }, D = async (k, E) => te(() => {\n        var C, z;\n        return d.chat(l.id, E, {\n          chatMode: a.chatMode,\n          streamId: (C = a.streamingManager) == null ? void 0 : C.streamId,\n          sessionId: (z = a.streamingManager) == null ? void 0 : z.sessionId,\n          messages: k.map(({\n            matches: F,\n            ...J\n          }) => J)\n        }, {\n          ...he(a.chatMode),\n          skipErrorHandler: !0\n        });\n      }, {\n        limit: 2,\n        shouldRetryFn: (C) => {\n          var J, f, g, S;\n          const z = (J = C == null ? void 0 : C.message) == null ? void 0 : J.includes(\"missing or invalid session_id\");\n          return !((f = C == null ? void 0 : C.message) == null ? void 0 : f.includes(\"Stream Error\")) && !z ? ((S = (g = t.callbacks).onError) == null || S.call(g, C), !1) : !0;\n        },\n        onRetry: async () => {\n          await x(), await j(!1);\n        }\n      });\n      try {\n        _(), v(), a.messages.push({\n          id: V(),\n          role: \"user\",\n          content: u,\n          created_at: new Date(N.update()).toISOString()\n        }), (p = (w = t.callbacks).onNewMessage) == null || p.call(w, [...a.messages], \"user\");\n        const k = await m(), E = await D([...a.messages], k);\n        return a.messages.push({\n          id: V(),\n          role: \"assistant\",\n          content: E.result || \"\",\n          created_at: (/* @__PURE__ */ new Date()).toISOString(),\n          context: E.context,\n          matches: E.matches\n        }), i.track(\"agent-message-send\", {\n          event: \"success\",\n          messages: a.messages.length + 1\n        }), E.result && ((L = (b = t.callbacks).onNewMessage) == null || L.call(b, [...a.messages], \"answer\"), i.track(\"agent-message-received\", {\n          latency: N.get(!0),\n          messages: a.messages.length\n        })), E;\n      } catch (k) {\n        throw (($ = a.messages[a.messages.length - 1]) == null ? void 0 : $.role) === \"assistant\" && a.messages.pop(), i.track(\"agent-message-send\", {\n          event: \"error\",\n          messages: a.messages.length\n        }), k;\n      }\n    },\n    rate(u, v, m) {\n      var p, b, L, $;\n      const D = a.messages.find((k) => k.id === u);\n      if (a.chat) {\n        if (!D)\n          throw new Error(\"Message not found\");\n      } else\n        throw new Error(\"Chat is not initialized\");\n      const w = ((p = D.matches) == null ? void 0 : p.map((k) => [k.document_id, k.id])) ?? [];\n      return i.track(\"agent-rate\", {\n        event: m ? \"update\" : \"create\",\n        thumb: v === 1 ? \"up\" : \"down\",\n        knowledge_id: ((b = l.knowledge) == null ? void 0 : b.id) ?? \"\",\n        matches: w,\n        score: v\n      }), m ? d.updateRating(l.id, a.chat.id, m, {\n        knowledge_id: ((L = l.knowledge) == null ? void 0 : L.id) ?? \"\",\n        message_id: u,\n        matches: w,\n        score: v\n      }) : d.createRating(l.id, a.chat.id, {\n        knowledge_id: (($ = l.knowledge) == null ? void 0 : $.id) ?? \"\",\n        message_id: u,\n        matches: w,\n        score: v\n      });\n    },\n    deleteRate(u) {\n      if (!a.chat)\n        throw new Error(\"Chat is not initialized\");\n      return i.track(\"agent-rate-delete\", {\n        type: \"text\"\n      }), d.deleteRating(l.id, a.chat.id, u);\n    },\n    async speak(u) {\n      var w, p, b;\n      function v() {\n        if (typeof u == \"string\") {\n          if (!l.presenter.voice)\n            throw new Error(\"Presenter voice is not initialized\");\n          return {\n            type: \"text\",\n            provider: l.presenter.voice,\n            input: u,\n            ssml: !1\n          };\n        }\n        if (u.type === \"text\" && !u.provider) {\n          if (!l.presenter.voice)\n            throw new Error(\"Presenter voice is not initialized\");\n          return {\n            type: \"text\",\n            provider: l.presenter.voice,\n            input: u.input,\n            ssml: u.ssml\n          };\n        }\n        return u;\n      }\n      const m = v();\n      if (i.track(\"agent-speak\", m), N.update(), a.messages && m.type === \"text\" && (a.messages.push({\n        id: V(),\n        role: \"assistant\",\n        content: m.input,\n        created_at: new Date(N.get(!0)).toISOString()\n      }), (p = (w = t.callbacks).onNewMessage) == null || p.call(w, [...a.messages], \"answer\")), ze(a.chatMode))\n        return {\n          duration: 0,\n          video_id: \"\",\n          status: \"success\"\n        };\n      if (!a.streamingManager)\n        throw new Error(\"Please connect to the agent first\");\n      return a.streamingManager.speak({\n        script: m,\n        metadata: {\n          chat_id: (b = a.chat) == null ? void 0 : b.id,\n          agent_id: l.id\n        }\n      });\n    },\n    async interrupt({\n      type: u\n    }) {\n      var m, D, w;\n      Oe(a.streamingManager, (m = a.streamingManager) == null ? void 0 : m.streamType, r);\n      const v = a.messages[a.messages.length - 1];\n      i.track(\"agent-video-interrupt\", {\n        type: u || \"click\",\n        video_duration_to_interrupt: re.get(!0),\n        message_duration_to_interrupt: N.get(!0)\n      }), v.interrupted = !0, (w = (D = t.callbacks).onNewMessage) == null || w.call(D, [...a.messages], \"answer\"), Xe(a.streamingManager, r);\n    }\n  };\n}\nfunction Mt(e, t, n) {\n  const {\n    getById: r\n  } = fe(t, n || Z);\n  return r(e);\n}\nexport {\n  O as AgentActivityState,\n  Ie as AgentStatus,\n  ke as ChatCreationFailed,\n  M as ChatMode,\n  De as ChatModeDowngraded,\n  q as ChatProgress,\n  I as ConnectionState,\n  Q as ConnectivityState,\n  Te as DocumentType,\n  _e as KnowledgeType,\n  Se as PlanGroup,\n  Pe as Providers,\n  Ee as RateState,\n  A as StreamEvents,\n  W as StreamType,\n  h as StreamingState,\n  Me as Subject,\n  Re as UserPlan,\n  H as ValidationError,\n  je as VideoType,\n  be as VoiceAccess,\n  Ce as WsError,\n  Et as createAgentManager,\n  Mt as getAgent,\n  It as mapVideoType\n};\n"], "mappings": ";AAAA,IAAI,KAAK,OAAO;AAChB,IAAI,KAAK,CAAC,GAAG,GAAG,MAAM,KAAK,IAAI,GAAG,GAAG,GAAG,EAAE,YAAY,MAAI,cAAc,MAAI,UAAU,MAAI,OAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI;AAC/G,IAAI,IAAI,CAAC,GAAG,GAAG,OAAO,GAAG,GAAG,OAAO,KAAK,WAAW,IAAI,KAAK,GAAG,CAAC,GAAG;AACnE,IAAM,IAAN,cAAgB,MAAM;AAAA,EACpB,YAAY;AAAA,IACV,MAAM;AAAA,IACN,aAAa;AAAA,IACb,OAAO;AAAA,EACT,GAAG;AACD,UAAM,KAAK,UAAU;AAAA,MACnB,MAAM;AAAA,MACN,aAAa;AAAA,IACf,CAAC,CAAC;AACF,MAAE,MAAM,MAAM;AACd,MAAE,MAAM,aAAa;AACrB,MAAE,MAAM,OAAO;AACf,SAAK,OAAO,GAAG,KAAK,cAAc,GAAG,KAAK,QAAQ;AAAA,EACpD;AACF;AACA,IAAM,KAAN,cAAiB,EAAE;AAAA,EACjB,YAAY,GAAG,GAAG;AAChB,UAAM;AAAA,MACJ,MAAM;AAAA,MACN,aAAa,oBAAoB,IAAI,eAAe,EAAE,gBAAgB,CAAC;AAAA,IACzE,CAAC;AAAA,EACH;AACF;AACA,IAAM,KAAN,cAAiB,EAAE;AAAA,EACjB,YAAY,GAAG;AACb,UAAM;AAAA,MACJ,MAAM;AAAA,MACN,aAAa,2BAA2B,CAAC;AAAA,IAC3C,CAAC;AAAA,EACH;AACF;AACA,IAAM,IAAN,cAAgB,EAAE;AAAA,EAChB,YAAY,GAAG,GAAG;AAChB,UAAM;AAAA,MACJ,MAAM;AAAA,MACN,aAAa;AAAA,IACf,CAAC;AACD,MAAE,MAAM,KAAK;AACb,SAAK,MAAM;AAAA,EACb;AACF;AACA,IAAM,KAAN,cAAiB,EAAE;AAAA,EACjB,YAAY,GAAG;AACb,UAAM;AAAA,MACJ,MAAM;AAAA,MACN,aAAa;AAAA,IACf,CAAC;AAAA,EACH;AACF;AACA,IAAI,MAAsB,CAAC,OAAO,EAAE,QAAQ,SAAS,EAAE,QAAQ,SAAS,EAAE,aAAa,cAAc,EAAE,OAAO,QAAQ,EAAE,WAAW,YAAY,IAAI,MAAM,CAAC,CAAC;AAA3J,IAA8J,MAAsB,CAAC,OAAO,EAAE,QAAQ,cAAc,EAAE,MAAM,YAAY,EAAE,aAAa,mBAAmB,EAAE,OAAO,aAAa,EAAE,WAAW,iBAAiB,EAAE,QAAQ,kBAAkB,EAAE,SAAS,mBAAmB,EAAE,QAAQ,kBAAkB,IAAI,MAAM,CAAC,CAAC;AAAha,IAAma,MAAsB,CAAC,OAAO,EAAE,UAAU,WAAW,EAAE,UAAU,WAAW,EAAE,OAAO,QAAQ,EAAE,QAAQ,SAAS,EAAE,WAAW,YAAY,EAAE,QAAQ,SAAS,IAAI,MAAM,CAAC,CAAC;AAA3kB,IAA8kB,MAAsB,CAAC,OAAO,EAAE,UAAU,WAAW,EAAE,WAAW,YAAY,EAAE,WAAW,YAAY,IAAI,MAAM,CAAC,CAAC;AAAjsB,IAAosB,KAAqB,CAAC,OAAO,EAAE,aAAa,cAAc,EAAE,WAAW,YAAY,EAAE,cAAc,eAAe,EAAE,aAAa,cAAc,EAAE,iBAAiB,kBAAkB,EAAE,MAAM,OAAO,IAAI,KAAK,CAAC,CAAC;AAAl5B,IAAq5B,KAAqB,CAAC,OAAO,EAAE,QAAQ,SAAS,EAAE,QAAQ,SAAS,EAAE,UAAU,WAAW,EAAE,SAAS,UAAU,EAAE,WAAW,QAAQ,IAAI,KAAK,CAAC,CAAC;AAApiC,IAAuiC,MAAsB,CAAC,OAAO,EAAE,sBAAsB,wBAAwB,EAAE,oBAAoB,sBAAsB,EAAE,kBAAkB,mBAAmB,EAAE,gBAAgB,kBAAkB,IAAI,MAAM,CAAC,CAAC;AAAxvC,IAA2vC,MAAsB,CAAC,OAAO,EAAE,YAAY,aAAa,EAAE,WAAW,YAAY,EAAE,SAAS,UAAU,IAAI,MAAM,CAAC,CAAC;AAA92C,IAAi3C,MAAsB,CAAC,OAAO,EAAE,MAAM,OAAO,EAAE,OAAO,QAAQ,EAAE,OAAO,QAAQ,EAAE,OAAO,QAAQ,EAAE,OAAO,QAAQ,EAAE,WAAW,YAAY,EAAE,MAAM,OAAO,EAAE,QAAQ,SAAS,EAAE,aAAa,cAAc,EAAE,UAAU,WAAW,EAAE,QAAQ,SAAS,EAAE,QAAQ,SAAS,EAAE,QAAQ,SAAS,IAAI,MAAM,CAAC,CAAC;AAAtpD,IAAypD,MAAsB,CAAC,OAAO,EAAE,OAAO,QAAQ,EAAE,OAAO,QAAQ,IAAI,MAAM,CAAC,CAAC;AACruD,IAAM,KAAK,CAAC,MAAM;AAChB,UAAQ,GAAG;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT;AACE,YAAM,IAAI,MAAM,uBAAuB,CAAC,EAAE;AAAA,EAC9C;AACF;AACA,IAAI,KAAqB,CAAC,OAAO,EAAE,QAAQ,SAAS,EAAE,OAAO,QAAQ,IAAI,KAAK,CAAC,CAAC;AAAhF,IAAmF,KAAqB,CAAC,OAAO,EAAE,SAAS,UAAU,EAAE,OAAO,QAAQ,EAAE,UAAU,WAAW,IAAI,KAAK,CAAC,CAAC;AAAxL,IAA2L,KAAqB,CAAC,OAAO,EAAE,OAAO,QAAQ,EAAE,UAAU,WAAW,IAAI,KAAK,CAAC,CAAC;AAA3Q,IAA8Q,KAAqB,CAAC,OAAO,EAAE,aAAa,eAAe,EAAE,cAAc,gBAAgB,EAAE,aAAa,eAAe,EAAE,gBAAgB,kBAAkB,EAAE,eAAe,gBAAgB,EAAE,cAAc,gBAAgB,EAAE,gBAAgB,kBAAkB,EAAE,kBAAkB,oBAAoB,EAAE,qBAAqB,wBAAwB,EAAE,kBAAkB,qBAAqB,EAAE,mBAAmB,sBAAsB,EAAE,sBAAsB,yBAAyB,IAAI,KAAK,CAAC,CAAC;AAAvvB,IAA0vB,KAAqB,CAAC,OAAO,EAAE,MAAM,OAAO,EAAE,OAAO,QAAQ,EAAE,YAAY,aAAa,EAAE,aAAa,cAAc,EAAE,SAAS,UAAU,EAAE,YAAY,aAAa,EAAE,eAAe,gBAAgB,IAAI,KAAK,CAAC,CAAC;AAA38B,IAA88B,KAAqB,CAAC,OAAO,EAAE,SAAS,UAAU,EAAE,SAAS,UAAU,IAAI,KAAK,CAAC,CAAC;AAAhiC,IAAmiC,MAAsB,CAAC,OAAO,EAAE,SAAS,UAAU,EAAE,YAAY,aAAa,EAAE,gBAAgB,iBAAiB,EAAE,aAAa,cAAc,IAAI,MAAM,CAAC,CAAC;AAA7rC,IAAgsC,MAAsB,CAAC,OAAO,EAAE,SAAS,UAAU,EAAE,UAAU,WAAW,EAAE,UAAU,WAAW,IAAI,MAAM,CAAC,CAAC;AAC7yC,IAAM,KAAK,KAAK;AAAhB,IAAqB,KAAK;AAA1B,IAA+C,IAAI;AAAnD,IAA2E,KAAK;AAAhF,IAAgH,KAAK;AAArH,IAAyJ,KAAK,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM,WAAW,GAAG,CAAC,CAAC;AAAxM,IAA2M,IAAI,CAAC,IAAI,OAAO;AACzN,QAAM,IAAI,IAAI,WAAW,CAAC;AAC1B,SAAO,OAAO,OAAO,gBAAgB,CAAC,GAAG,MAAM,KAAK,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,GAAG,EAAE;AACrH;AAHA,IAGG,KAAK,CAAC,MAAM,CAAC,EAAE,UAAU,EAAE,YAAY,EAAE,WAAW,EAAE,SAAS,CAAC;AAHnE,IAGsE,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,gBAAgB,EAAE,GAAG,EAAE,SAAS,CAAC;AAC3H,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI;AACJ,SAAO;AAAA,IACL,SAAS,IAAI,QAAQ,CAAC,GAAG,MAAM;AAC7B,UAAI,WAAW,MAAM,EAAE,IAAI,MAAM,CAAC,CAAC,GAAG,CAAC;AAAA,IACzC,CAAC;AAAA,IACD,OAAO,MAAM,aAAa,CAAC;AAAA,EAC7B;AACF;AACA,eAAe,GAAG,GAAG,GAAG;AACtB,QAAM,IAAI;AAAA,IACR,QAAQ,KAAK,OAAO,SAAS,EAAE,UAAU;AAAA,IACzC,UAAU,KAAK,OAAO,SAAS,EAAE,YAAY;AAAA,IAC7C,UAAU,KAAK,OAAO,SAAS,EAAE,YAAY;AAAA,IAC7C,sBAAsB,KAAK,OAAO,SAAS,EAAE,wBAAwB;AAAA,IACrE,gBAAgB,KAAK,OAAO,SAAS,EAAE,mBAAmB,MAAM;AAAA,IAChE,UAAU,KAAK,OAAO,SAAS,EAAE,aAAa,MAAM;AAAA,IACpD;AAAA,EACF;AACA,MAAI;AACJ,WAAS,IAAI,GAAG,KAAK,EAAE,OAAO;AAC5B,QAAI;AACF,UAAI,CAAC,EAAE;AACL,eAAO,MAAM,EAAE;AACjB,YAAM;AAAA,QACJ,SAAS;AAAA,QACT,OAAO;AAAA,MACT,IAAI,GAAG,EAAE,SAAS,EAAE,mBAAmB,GAAG,IAAI,EAAE,EAAE,QAAQ,CAAC;AAC3D,aAAO,MAAM,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,IAClC,SAAS,GAAG;AACV,UAAI,IAAI,GAAG,CAAC,EAAE,cAAc,CAAC,KAAK,KAAK,EAAE;AACvC,cAAM;AACR,YAAM,GAAG,EAAE,OAAO,GAAG,EAAE,QAAQ,CAAC;AAAA,IAClC;AACF,QAAM;AACR;AACA,SAAS,KAAK;AACZ,MAAI,IAAI,OAAO,aAAa,QAAQ,qBAAqB;AACzD,MAAI,CAAC,GAAG;AACN,QAAI,IAAI,EAAE;AACV,WAAO,aAAa,QAAQ,uBAAuB,CAAC,GAAG,IAAI;AAAA,EAC7D;AACA,SAAO;AACT;AACA,IAAI,KAAK,EAAE;AACX,SAAS,GAAG,GAAG;AACb,MAAI,EAAE,SAAS;AACb,WAAO,UAAU,EAAE,KAAK;AAC1B,MAAI,EAAE,SAAS;AACb,WAAO,SAAS,KAAK,GAAG,EAAE,QAAQ,IAAI,EAAE,QAAQ,EAAE,CAAC;AACrD,MAAI,EAAE,SAAS;AACb,WAAO,cAAc,EAAE,SAAS,IAAI,GAAG,CAAC,IAAI,EAAE;AAChD,QAAM,IAAI,MAAM,sBAAsB,CAAC,EAAE;AAC3C;AACA,IAAM,KAAK,CAAC,MAAM,GAAG,GAAG;AAAA,EACtB,OAAO;AAAA,EACP,SAAS;AAAA,EACT,SAAS;AAAA,EACT,eAAe,CAAC,MAAM,EAAE,WAAW;AACrC,CAAC;AACD,SAAS,GAAG,GAAG,IAAI,GAAG,GAAG;AACvB,QAAM,IAAI,OAAO,GAAG,MAAM;AACxB,UAAM;AAAA,MACJ,kBAAkB;AAAA,MAClB,GAAG;AAAA,IACL,IAAI,KAAK,CAAC,GAAG,IAAI,MAAM,GAAG,MAAM,MAAM,KAAK,KAAK,QAAQ,EAAE,WAAW,GAAG,IAAI,IAAI,IAAI,CAAC,KAAK;AAAA,MACxF,GAAG;AAAA,MACH,SAAS;AAAA,QACP,GAAG,EAAE;AAAA,QACL,eAAe,GAAG,CAAC;AAAA,QACnB,gBAAgB;AAAA,MAClB;AAAA,IACF,CAAC,CAAC;AACF,QAAI,CAAC,EAAE,IAAI;AACT,UAAI,IAAI,MAAM,EAAE,KAAK,EAAE,MAAM,MAAM,+BAA+B,EAAE,MAAM,EAAE;AAC5E,YAAM,IAAI,IAAI,MAAM,CAAC;AACrB,YAAM,KAAK,CAAC,KAAK,EAAE,GAAG;AAAA,QACpB,KAAK;AAAA,QACL,SAAS;AAAA,QACT,SAAS,EAAE;AAAA,MACb,CAAC,GAAG;AAAA,IACN;AACA,WAAO,EAAE,KAAK;AAAA,EAChB;AACA,SAAO;AAAA,IACL,IAAI,GAAG,GAAG;AACR,aAAO,EAAE,GAAG;AAAA,QACV,GAAG;AAAA,QACH,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAAA,IACA,KAAK,GAAG,GAAG,GAAG;AACZ,aAAO,EAAE,GAAG;AAAA,QACV,GAAG;AAAA,QACH,MAAM,KAAK,UAAU,CAAC;AAAA,QACtB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAAA,IACA,OAAO,GAAG,GAAG,GAAG;AACd,aAAO,EAAE,GAAG;AAAA,QACV,GAAG;AAAA,QACH,MAAM,KAAK,UAAU,CAAC;AAAA,QACtB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAAA,IACA,MAAM,GAAG,GAAG,GAAG;AACb,aAAO,EAAE,GAAG;AAAA,QACV,GAAG;AAAA,QACH,MAAM,KAAK,UAAU,CAAC;AAAA,QACtB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAAA,EACF;AACF;AACA,SAAS,GAAG,GAAG,IAAI,GAAG,GAAG;AACvB,QAAM,IAAI,GAAG,GAAG,GAAG,CAAC,WAAW,CAAC;AAChC,SAAO;AAAA,IACL,OAAO,GAAG,GAAG;AACX,aAAO,EAAE,KAAK,KAAK,GAAG,CAAC;AAAA,IACzB;AAAA,IACA,UAAU,GAAG,GAAG;AACd,aAAO,EAAE,IAAI,IAAI,IAAI,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC;AAAA,IACjE;AAAA,IACA,QAAQ,GAAG,GAAG;AACZ,aAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC;AAAA,IACzB;AAAA,IACA,OAAO,GAAG,GAAG;AACX,aAAO,EAAE,OAAO,IAAI,CAAC,IAAI,QAAQ,CAAC;AAAA,IACpC;AAAA,IACA,OAAO,GAAG,GAAG,GAAG;AACd,aAAO,EAAE,MAAM,IAAI,CAAC,IAAI,GAAG,CAAC;AAAA,IAC9B;AAAA,IACA,QAAQ,GAAG,GAAG,GAAG;AACf,aAAO,EAAE,KAAK,IAAI,CAAC,SAAS,GAAG,CAAC;AAAA,IAClC;AAAA,IACA,KAAK,GAAG,GAAG,GAAG,GAAG;AACf,aAAO,EAAE,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC;AAAA,IACvC;AAAA,IACA,aAAa,GAAG,GAAG,GAAG,GAAG;AACvB,aAAO,EAAE,KAAK,IAAI,CAAC,SAAS,CAAC,YAAY,GAAG,CAAC;AAAA,IAC/C;AAAA,IACA,aAAa,GAAG,GAAG,GAAG,GAAG,GAAG;AAC1B,aAAO,EAAE,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC;AAAA,IACrD;AAAA,IACA,aAAa,GAAG,GAAG,GAAG,GAAG;AACvB,aAAO,EAAE,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC;AAAA,IACnD;AAAA,IACA,YAAY,GAAG,GAAG;AAChB,aAAO,EAAE,IAAI,IAAI,CAAC,cAAc,CAAC;AAAA,IACnC;AAAA,EACF;AACF;AACA,IAAM,KAAK,CAAC,MAAM,EAAE,SAAS,UAAU,EAAE,aAAa,WAAW,KAAK,IAAI,YAAY,EAAE;AACxF,SAAS,GAAG,GAAG;AACb,MAAI,GAAG,GAAG,GAAG;AACb,QAAM,IAAI,MAAM,gBAAgB,KAAK,UAAU,SAAS,IAAI,WAAW,WAAW,IAAI,MAAM;AAC1F,UAAM,IAAI,UAAU;AACpB,WAAO,EAAE,YAAY,EAAE,SAAS,KAAK,IAAI,YAAY,EAAE,YAAY,EAAE,SAAS,KAAK,IAAI,aAAa,EAAE,YAAY,EAAE,SAAS,OAAO,IAAI,UAAU;AAAA,EACpJ,GAAG,IAAI,EAAE;AACT,SAAO;AAAA,IACL,KAAK,GAAG,EAAE,CAAC;AAAA,IACX,UAAU,GAAG,EAAE,KAAK,QAAQ;AAAA,IAC5B,SAAS,UAAU;AAAA,IACnB,QAAQ,OAAO,SAAS;AAAA,IACxB,WAAW,GAAG,CAAC;AAAA,IACf,YAAY;AAAA,MACV,UAAU,KAAK,IAAI,EAAE,cAAc,OAAO,SAAS,EAAE,UAAU,OAAO,SAAS,EAAE;AAAA,MACjF,WAAW,KAAK,IAAI,EAAE,cAAc,OAAO,SAAS,EAAE,UAAU,OAAO,SAAS,EAAE;AAAA,IACpF;AAAA,EACF;AACF;AACA,SAAS,GAAG,GAAG;AACb,MAAI,GAAG,GAAG,GAAG,GAAG,GAAG;AACnB,QAAM,KAAK,IAAI,EAAE,QAAQ,OAAO,SAAS,EAAE;AAC3C,SAAO;AAAA,IACL,WAAW,GAAG,EAAE,SAAS;AAAA,IACzB,UAAU,EAAE,YAAY;AAAA,IACxB,gBAAgB,IAAI,EAAE,QAAQ,OAAO,SAAS,EAAE;AAAA,IAChD,UAAU;AAAA,MACR,MAAM,KAAK,OAAO,SAAS,EAAE;AAAA,MAC7B,aAAa,KAAK,OAAO,SAAS,EAAE;AAAA,MACpC,eAAe,IAAI,EAAE,QAAQ,OAAO,SAAS,EAAE;AAAA,IACjD;AAAA,IACA,cAAc,IAAI,EAAE,QAAQ,OAAO,SAAS,EAAE;AAAA,IAC9C,iBAAiB,KAAK,OAAO,SAAS,EAAE;AAAA,IACxC,wBAAwB,KAAK,IAAI,EAAE,cAAc,OAAO,SAAS,EAAE,oBAAoB,OAAO,SAAS,EAAE;AAAA,IACzG,eAAe,KAAK,OAAO,SAAS,EAAE;AAAA,IACtC,mBAAmB,KAAK,OAAO,SAAS,EAAE;AAAA,IAC1C,SAAS,EAAE;AAAA,IACX,QAAQ,EAAE;AAAA,IACV,MAAM,EAAE;AAAA,IACR,GAAG,EAAE,WAAW,WAAW;AAAA,MACzB,MAAM;AAAA,IACR,IAAI,CAAC;AAAA,EACP;AACF;AACA,IAAM,KAAK,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC;AAA7C,IAAgD,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,EAAE;AACtE,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,MAAI,GAAG,GAAG;AACV,QAAM;AAAA,IACJ,OAAO;AAAA,IACP,GAAG;AAAA,EACL,IAAI,GAAG;AAAA,IACL,UAAU;AAAA,EACZ,KAAK,KAAK,OAAO,SAAS,EAAE,QAAQ,CAAC,GAAG;AAAA,IACtC,UAAU;AAAA,EACZ,MAAM,IAAI,KAAK,OAAO,SAAS,EAAE,cAAc,OAAO,SAAS,EAAE,UAAU,CAAC;AAC5E,SAAO;AAAA,IACL,GAAG;AAAA,IACH,KAAK;AAAA,MACH,GAAG,EAAE;AAAA,MACL,UAAU;AAAA,IACZ;AAAA,IACA,QAAQ;AAAA,MACN,GAAG,EAAE;AAAA,MACL,UAAU;AAAA,QACR,IAAI,IAAI,KAAK,OAAO,SAAS,EAAE,WAAW,OAAO,SAAS,EAAE;AAAA,QAC5D,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,IACA,SAAS,KAAK,OAAO,SAAS,EAAE,UAAU,UAAU,UAAU,IAAI,KAAK,OAAO,SAAS,EAAE,cAAc,OAAO,SAAS,EAAE,SAAS;AAAA,IAClI,GAAG;AAAA,EACL;AACF;AACA,IAAI,KAAK,CAAC;AACV,IAAM,KAAK;AACX,SAAS,GAAG,GAAG;AACb,QAAM,IAAI,UAAU,QAAQ,OAAO,eAAe,gBAAgB,IAAI,cAAc;AACpF,SAAO;AAAA,IACL,OAAO,EAAE,SAAS;AAAA,IAClB,aAAa,EAAE,cAAc,GAAG;AAAA,IAChC,SAAS,EAAE;AAAA,IACX,sBAAsB,CAAC;AAAA,IACvB,WAAW,EAAE,aAAa;AAAA,IAC1B,WAAW;AAAA,IACX,OAAO,GAAG;AACR,WAAK,uBAAuB;AAAA,QAC1B,GAAG,KAAK;AAAA,QACR,GAAG;AAAA,MACL;AAAA,IACF;AAAA,IACA,MAAM,MAAM,GAAG,GAAG;AAChB,UAAI,CAAC,KAAK;AACR,eAAO,QAAQ,QAAQ;AACzB,YAAM;AAAA,QACJ,WAAW;AAAA,QACX,GAAG;AAAA,MACL,IAAI,KAAK,CAAC,GAAG,IAAI;AAAA,QACf,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,gBAAgB;AAAA,QAClB;AAAA,QACA,MAAM,IAAI,gBAAgB;AAAA,UACxB,MAAM,KAAK,UAAU,CAAC;AAAA,YACpB,OAAO;AAAA,YACP,YAAY;AAAA,cACV,GAAG,KAAK;AAAA,cACR,GAAG;AAAA,cACH,SAAS,KAAK;AAAA,cACd,QAAQ;AAAA,cACR,OAAO,KAAK;AAAA,cACZ,MAAM,KAAK,IAAI;AAAA,cACf,YAAY,KAAK,UAAU;AAAA,cAC3B,QAAQ,OAAO,SAAS;AAAA,cACxB,iBAAiB,OAAO,OAAO,UAAU,OAAO;AAAA,cAChD,gBAAgB,OAAO,OAAO,SAAS,OAAO;AAAA,cAC9C,cAAc,UAAU;AAAA,YAC1B;AAAA,UACF,CAAC,CAAC;AAAA,QACJ,CAAC;AAAA,MACH;AACA,UAAI;AACF,eAAO,MAAM,MAAM,IAAI,CAAC,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC;AAAA,MAChD,SAAS,GAAG;AACV,eAAO,QAAQ,MAAM,CAAC;AAAA,MACxB;AAAA,IACF;AAAA,IACA,UAAU,GAAG,GAAG,GAAG,GAAG;AACpB,SAAG,CAAC,MAAM,GAAG,CAAC,IAAI;AAAA,QAChB,QAAQ,CAAC;AAAA,QACT,sBAAsB,CAAC;AAAA,MACzB,IAAI,EAAE,SAAS,CAAC,KAAK,EAAE,KAAK,CAAC;AAC7B,YAAM,IAAI,GAAG,CAAC;AACd,UAAI,EAAE,OAAO,CAAC,IAAI;AAAA,QAChB,OAAO;AAAA,MACT,GAAG,EAAE,qBAAqB,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,MAAM,EAAE,qBAAqB,SAAS,CAAC,CAAC,GAAG;AACrF,cAAM,IAAI,EAAE,OAAO,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC,IAAI;AAAA,UACzC,GAAG;AAAA,UACH,GAAG,EAAE,OAAO,CAAC,EAAE;AAAA,QACjB,IAAI,GAAG,CAAC,CAAC;AACT,aAAK,MAAM,GAAG,CAAC,GAAG,EAAE,uBAAuB,EAAE,qBAAqB,OAAO,CAAC,MAAM,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,EAAE,QAAQ,CAAC,MAAM;AAChH,iBAAO,EAAE,OAAO,CAAC;AAAA,QACnB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,KAAK;AACZ,MAAI,IAAI;AACR,SAAO;AAAA,IACL,OAAO,MAAM,IAAI;AAAA,IACjB,QAAQ,MAAM,IAAI,KAAK,IAAI;AAAA,IAC3B,KAAK,CAAC,IAAI,UAAO,IAAI,KAAK,IAAI,IAAI,IAAI;AAAA,EACxC;AACF;AACA,IAAM,IAAI,GAAG;AAAb,IAAgB,KAAK,GAAG;AACxB,SAAS,GAAG,GAAG;AACb,SAAO,MAAM,EAAE,aAAa;AAAA,IAC1B,SAAS;AAAA,MACP,CAAC,EAAE,GAAG;AAAA,IACR;AAAA,EACF,IAAI,CAAC;AACP;AACA,eAAe,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,OAAI,GAAG;AACvC,MAAI;AACF,WAAO,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,IAAI,MAAM,EAAE,QAAQ,EAAE,IAAI;AAAA,MAChD,SAAS;AAAA,IACX,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,MAAM,cAAc;AAAA,MAC/B,OAAO;AAAA,MACP,QAAQ,EAAE;AAAA,MACV,MAAM;AAAA,IACR,CAAC,IAAI;AAAA,MACH,MAAM;AAAA,MACN,WAAW,KAAK,OAAO,SAAS,EAAE,cAAc;AAAA,IAClD;AAAA,EACF,SAAS,GAAG;AACV,QAAI;AACF,YAAM,IAAI,KAAK,MAAM,EAAE,OAAO;AAC9B,WAAK,KAAK,OAAO,SAAS,EAAE,UAAU;AACpC,cAAM,IAAI,MAAM,0BAA0B;AAAA,IAC9C,SAAS,GAAG;AACV,cAAQ,MAAM,oCAAoC,CAAC;AAAA,IACrD;AACA,UAAM,IAAI,MAAM,wBAAwB;AAAA,EAC1C;AACF;AACA,SAAS,GAAG,GAAG;AACb,SAAO,KAAK,EAAE,SAAS,IAAI,IAAI,CAAC;AAClC;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,MAAI,CAAC;AACH,UAAM,IAAI,MAAM,mCAAmC;AACrD,MAAI,CAAC,EAAE;AACL,UAAM,IAAI,MAAM,0CAA0C;AAC5D,MAAI,MAAM,EAAE;AACV,UAAM,IAAI,MAAM,6CAA6C;AAC/D,MAAI,CAAC;AACH,UAAM,IAAI,MAAM,8BAA8B;AAClD;AACA,eAAe,GAAG,GAAG,GAAG;AACtB,QAAM,IAAI;AAAA,IACR,MAAM,EAAE;AAAA,IACR,SAAS;AAAA,IACT,WAAW,KAAK,IAAI;AAAA,EACtB;AACA,IAAE,uBAAuB,KAAK,UAAU,CAAC,CAAC;AAC5C;AACA,SAAS,GAAG,GAAG;AACb,SAAO,IAAI,QAAQ,CAAC,GAAG,MAAM;AAC3B,UAAM;AAAA,MACJ,WAAW;AAAA,MACX,MAAM;AAAA,MACN,MAAM;AAAA,IACR,IAAI,GAAG;AAAA,MACL,WAAW,IAAI;AAAA,MACf,QAAQ,IAAI;AAAA,MACZ,SAAS,IAAI;AAAA,MACb,SAAS,IAAI;AAAA,IACf,IAAI,KAAK,CAAC,GAAG,IAAI,IAAI,UAAU,GAAG,CAAC,kBAAkB,GAAG,CAAC,CAAC,EAAE;AAC5D,MAAE,YAAY,GAAG,EAAE,UAAU,GAAG,EAAE,UAAU,CAAC,MAAM;AACjD,cAAQ,MAAM,CAAC,GAAG,KAAK,QAAQ,EAAE,+BAA+B,CAAC,GAAG,EAAE,CAAC;AAAA,IACzE,GAAG,EAAE,SAAS,CAAC,MAAM;AACnB,WAAK,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC;AAAA,IACxB;AAAA,EACF,CAAC;AACH;AACA,eAAe,GAAG,GAAG;AACnB,QAAM;AAAA,IACJ,SAAS,IAAI;AAAA,EACf,IAAI;AACJ,MAAI,IAAI;AACR,WAAS,IAAI,IAAI,KAAK,OAAO,SAAS,EAAE,gBAAgB,UAAU,MAAM;AACtE,QAAI;AACF,UAAI,MAAM,GAAG,CAAC;AAAA,IAChB,SAAS,GAAG;AACV,UAAI,MAAM;AACR,cAAM;AACR,YAAM,GAAG,IAAI,GAAG;AAAA,IAClB;AACF,SAAO;AACT;AACA,eAAe,GAAG,GAAG,GAAG,GAAG;AACzB,QAAM,IAAI,KAAK,QAAQ,EAAE,YAAY,CAAC,EAAE,SAAS,IAAI,CAAC,GAAG,IAAI,MAAM,GAAG;AAAA,IACpE,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW;AAAA,MACT,SAAS,CAAC,MAAM;AACd,YAAI;AACJ,gBAAQ,IAAI,EAAE,YAAY,OAAO,SAAS,EAAE,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC;AAAA,MAC/D;AAAA,MACA,UAAU,GAAG;AACX,cAAM,IAAI,KAAK,MAAM,EAAE,IAAI;AAC3B,UAAE,QAAQ,CAAC,MAAM,EAAE,EAAE,OAAO,CAAC,CAAC;AAAA,MAChC;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO;AAAA,IACL,QAAQ;AAAA,IACR,YAAY,MAAM,EAAE,MAAM;AAAA,IAC1B,mBAAmB,CAAC,MAAM,EAAE,KAAK,CAAC;AAAA,EACpC;AACF;AACA,SAAS,GAAG,GAAG;AACb,MAAI,EAAE,WAAW;AACf,WAAO,EAAE;AACX,MAAI,IAAI,GAAG,IAAI;AACf,SAAO,KAAK;AACV,SAAK,EAAE,GAAG;AACZ,SAAO;AACT;AACA,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AACzB,QAAM,IAAI,EAAE,SAAS,EAAE,SAAS,SAAS,CAAC;AAC1C,MAAI,EAAE,MAAM,EAAE,WAAW,MAAM,EAAE,YAAY,KAAK,OAAO,SAAS,EAAE,UAAU;AAC5E;AACF,QAAM;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,IAAI;AACJ,QAAM,EAAE,UAAU,EAAE,CAAC,IAAI,IAAI,EAAE,SAAS;AACxC,QAAM,IAAI,GAAG,CAAC;AACd,GAAC,EAAE,YAAY,KAAK,MAAM,EAAE,YAAY,EAAE,UAAU,GAAG,KAAK,QAAQ,EAAE,CAAC,GAAG,EAAE,QAAQ,GAAG,CAAC;AAC1F;AACA,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AACzB,MAAI,IAAI,CAAC;AACT,SAAO;AAAA,IACL,YAAY,MAAM,IAAI,CAAC;AAAA,IACvB,WAAW,CAAC,GAAG,MAAM;AACnB,UAAI,GAAG;AACP,UAAI,aAAa;AACf,WAAG,GAAG,GAAG,GAAG,GAAG,EAAE,UAAU,YAAY,GAAG,MAAM,EAAE,UAAU,EAAE,MAAM,0BAA0B;AAAA,UAC5F,UAAU,EAAE,SAAS;AAAA,UACrB,MAAM,EAAE;AAAA,QACV,CAAC;AAAA,WACE;AACH,cAAM,IAAI,GAAG,IAAI,CAAC,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,mBAAmB,GAAG,IAAI,CAAC,EAAE,cAAc,EAAE,kBAAkB,EAAE,mBAAmB,GAAG,IAAI,GAAG,GAAG,GAAG;AAAA,UAC7J,MAAM,EAAE;AAAA,QACV,CAAC;AACD,YAAI,IAAI,GAAG,MAAM,EAAE;AACjB,YAAE,UAAU,eAAe,GAAG,EAAE,oBAAoB,CAAC,OAAO,CAAC;AAAA,iBACtD,EAAE,SAAS,CAAC,GAAG;AACtB,gBAAM,IAAI,EAAE,MAAM,GAAG,EAAE,CAAC;AACxB,YAAE,SAAS,CAAC,IAAI,EAAE,MAAM,eAAe;AAAA,YACrC,GAAG;AAAA,YACH,OAAO;AAAA,UACT,CAAC,IAAI,EAAE,UAAU,eAAe;AAAA,YAC9B,GAAG;AAAA,YACH,OAAO;AAAA,UACT,GAAG,GAAG,CAAC,MAAM,CAAC;AAAA,QAChB;AACA,UAAE,SAAS,CAAC,OAAO,KAAK,IAAI,EAAE,WAAW,YAAY,QAAQ,EAAE,KAAK,GAAG,IAAI,MAAM,4BAA4B,CAAC,EAAE,GAAG;AAAA,UACjH,MAAM;AAAA,QACR,CAAC,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE;AAAA,MACrC;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG;AACtB,QAAM,IAAI,GAAG,GAAG,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC;AACrC,SAAO;AAAA,IACL,aAAa,GAAG;AACd,aAAO,EAAE,KAAK,YAAY,CAAC;AAAA,IAC7B;AAAA,IACA,gBAAgB,GAAG,GAAG,GAAG;AACvB,aAAO,EAAE,KAAK,YAAY,CAAC,QAAQ;AAAA,QACjC,YAAY;AAAA,QACZ,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAAA,IACA,gBAAgB,GAAG,GAAG,GAAG;AACvB,aAAO,EAAE,KAAK,YAAY,CAAC,QAAQ;AAAA,QACjC,YAAY;AAAA,QACZ,GAAG;AAAA,MACL,CAAC;AAAA,IACH;AAAA,IACA,kBAAkB,GAAG,GAAG,GAAG;AACzB,aAAO,EAAE,KAAK,YAAY,CAAC,IAAI;AAAA,QAC7B,YAAY;AAAA,QACZ,GAAG;AAAA,MACL,CAAC;AAAA,IACH;AAAA,IACA,MAAM,GAAG,GAAG;AACV,aAAO,EAAE,OAAO,YAAY,CAAC,IAAI;AAAA,QAC/B,YAAY;AAAA,MACd,CAAC;AAAA,IACH;AAAA,EACF;AACF;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,QAAM,KAAK,EAAE,YAAY,EAAE,aAAa;AACxC,SAAO;AAAA,IACL,UAAU;AAAA,IACV,eAAe,EAAE,gBAAgB,EAAE;AAAA,IACnC,SAAS,KAAK,OAAO,EAAE,gBAAgB,EAAE,iBAAiB,IAAI,CAAC;AAAA,IAC/D,iBAAiB,EAAE,kBAAkB,EAAE;AAAA,IACvC,aAAa,EAAE,cAAc,EAAE;AAAA,IAC/B,eAAe,EAAE,gBAAgB,EAAE;AAAA,IACnC,eAAe,EAAE,gBAAgB,EAAE;AAAA,IACnC,QAAQ,EAAE;AAAA,IACV,2BAA2B,EAAE,oBAAoB,EAAE,sBAAsB,EAAE,2BAA2B,EAAE;AAAA,IACxG,0BAA0B,EAAE,2BAA2B,EAAE;AAAA,IACzD,oBAAoB,EAAE,oBAAoB,EAAE,qBAAqB;AAAA,IACjE,iBAAiB,EAAE;AAAA,IACnB,aAAa,EAAE,cAAc,EAAE;AAAA,IAC/B,gBAAgB,EAAE,iBAAiB,EAAE;AAAA,IACrC,aAAa;AAAA,EACf;AACF;AACA,SAAS,GAAG,GAAG;AACb,SAAO,EAAE,OAAO,CAAC,MAAM,EAAE,cAAc,KAAK,EAAE,kBAAkB,MAAM,EAAE,gBAAgB,KAAK,EAAE,cAAc,CAAC,EAAE,IAAI,CAAC,MAAM;AACzH,UAAM;AAAA,MACJ,WAAW;AAAA,MACX,GAAG;AAAA,IACL,IAAI,GAAG,IAAI,CAAC;AACZ,WAAO,EAAE,cAAc,KAAK,EAAE,KAAK,QAAQ,GAAG,EAAE,kBAAkB,MAAM,EAAE,KAAK,SAAS,GAAG,EAAE,gBAAgB,KAAK,EAAE,KAAK,gBAAgB,GAAG,EAAE,cAAc,KAAK,EAAE,KAAK,aAAa,GAAG;AAAA,MACtL,GAAG;AAAA,MACH,QAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACH;AACA,SAAS,GAAG,GAAG;AACb,MAAI,IAAI,IAAI,IAAI;AAChB,aAAW,KAAK,EAAE,OAAO;AACvB,QAAI,KAAK,EAAE,SAAS,WAAW,EAAE,SAAS,WAAW,OAAO,MAAM,IAAI,EAAE,SAAS,MAAM,GAAG,EAAE,CAAC,IAAI,KAAK,EAAE,SAAS,qBAAqB,IAAI,EAAE,uBAAuB,KAAK,EAAE,SAAS,iBAAiB,EAAE,SAAS;AAC7M,aAAO;AAAA,QACL,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW,EAAE;AAAA,QACb,eAAe,EAAE;AAAA,QACjB,iBAAiB,EAAE;AAAA,QACnB,aAAa,EAAE;AAAA,QACf,eAAe,EAAE;AAAA,QACjB,eAAe,EAAE;AAAA,QACjB,QAAQ,EAAE;AAAA,QACV,mBAAmB,EAAE;AAAA,QACrB,0BAA0B,EAAE;AAAA,QAC5B,0BAA0B,EAAE,oBAAoB,EAAE;AAAA,QAClD,YAAY,EAAE;AAAA,QACd,aAAa,EAAE;AAAA,QACf,iBAAiB,EAAE;AAAA,QACnB,aAAa,EAAE;AAAA,QACf,gBAAgB,EAAE;AAAA,MACpB;AACJ,SAAO,CAAC;AACV;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,QAAM,IAAI,EAAE,IAAI,CAAC,GAAG,MAAM,MAAM,IAAI,IAAI;AAAA,IACtC,WAAW,EAAE;AAAA,IACb,UAAU;AAAA,IACV,KAAK,EAAE;AAAA,IACP,eAAe,EAAE,gBAAgB,EAAE;AAAA,IACnC,UAAU,EAAE,gBAAgB,EAAE,iBAAiB,KAAK,IAAI;AAAA,IACxD,iBAAiB,EAAE,kBAAkB,EAAE;AAAA,IACvC,aAAa,EAAE,cAAc,EAAE;AAAA,IAC/B,eAAe,EAAE,gBAAgB,EAAE;AAAA,IACnC,eAAe,EAAE,gBAAgB,EAAE;AAAA,IACnC,QAAQ,EAAE;AAAA,IACV,mBAAmB,EAAE,oBAAoB,EAAE;AAAA,IAC3C,0BAA0B,EAAE,2BAA2B,EAAE;AAAA,IACzD,2BAA2B,EAAE,oBAAoB,EAAE,sBAAsB,EAAE,2BAA2B,EAAE;AAAA,IACxG,iBAAiB,EAAE;AAAA,IACnB,aAAa,EAAE,cAAc,EAAE;AAAA,IAC/B,gBAAgB,EAAE,iBAAiB,EAAE;AAAA,EACvC,IAAI;AAAA,IACF,WAAW,EAAE;AAAA,IACb,KAAK,EAAE;AAAA,IACP,UAAU;AAAA,IACV,eAAe,EAAE;AAAA,IACjB,SAAS,EAAE,gBAAgB,KAAK,IAAI;AAAA,IACpC,iBAAiB,EAAE;AAAA,IACnB,aAAa,EAAE;AAAA,IACf,eAAe,EAAE;AAAA,IACjB,eAAe,EAAE;AAAA,IACjB,QAAQ,EAAE;AAAA,IACV,mBAAmB,EAAE;AAAA,IACrB,0BAA0B,EAAE;AAAA,IAC5B,0BAA0B,EAAE,oBAAoB,EAAE;AAAA,IAClD,iBAAiB,EAAE;AAAA,IACnB,aAAa,EAAE;AAAA,IACf,gBAAgB,EAAE;AAAA,EACpB,IAAI;AAAA,IACF,WAAW,EAAE;AAAA,IACb,UAAU,IAAI,IAAI;AAAA,IAClB,KAAK,EAAE;AAAA,IACP,eAAe,EAAE,gBAAgB,EAAE,IAAI,CAAC,EAAE;AAAA,IAC1C,UAAU,EAAE,gBAAgB,EAAE,IAAI,CAAC,EAAE,iBAAiB,KAAK,IAAI;AAAA,IAC/D,iBAAiB,EAAE,kBAAkB,EAAE,IAAI,CAAC,EAAE;AAAA,IAC9C,aAAa,EAAE,cAAc,EAAE,IAAI,CAAC,EAAE;AAAA,IACtC,eAAe,EAAE,gBAAgB,EAAE,IAAI,CAAC,EAAE;AAAA,IAC1C,eAAe,EAAE,gBAAgB,EAAE,IAAI,CAAC,EAAE;AAAA,IAC1C,QAAQ,EAAE;AAAA,IACV,mBAAmB,EAAE,oBAAoB,EAAE,IAAI,CAAC,EAAE;AAAA,IAClD,0BAA0B,EAAE,2BAA2B,EAAE,IAAI,CAAC,EAAE;AAAA,IAChE,2BAA2B,EAAE,oBAAoB,EAAE,IAAI,CAAC,EAAE,sBAAsB,EAAE,2BAA2B,EAAE,IAAI,CAAC,EAAE;AAAA,IACtH,iBAAiB,EAAE;AAAA,IACnB,aAAa,EAAE,cAAc,EAAE,IAAI,CAAC,EAAE;AAAA,IACtC,gBAAgB,EAAE,iBAAiB,EAAE,IAAI,CAAC,EAAE;AAAA,EAC9C,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,EAAE,OAAO,CAAC,GAAG,MAAM,KAAK,EAAE,OAAO,SAAS,SAAS,IAAI,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,wBAAwB,EAAE,IAAI,CAAC,MAAM,EAAE,wBAAwB,GAAG,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG;AAC7N,SAAO;AAAA,IACL,aAAa;AAAA,MACX,WAAW;AAAA,MACX,QAAQ,KAAK,IAAI,GAAG,CAAC;AAAA,MACrB,QAAQ,GAAG,CAAC;AAAA,MACZ,QAAQ,KAAK,IAAI,GAAG,CAAC;AAAA,MACrB,iBAAiB,GAAG,EAAE,CAAC,GAAG,EAAE,EAAE,SAAS,CAAC,GAAG,CAAC;AAAA,MAC5C,0BAA0B,KAAK,IAAI,GAAG,CAAC;AAAA,MACvC,0BAA0B,KAAK,IAAI,GAAG,CAAC;AAAA,MACvC,0BAA0B,GAAG,CAAC;AAAA,IAChC;AAAA,IACA,OAAO,EAAE,CAAC,EAAE;AAAA,IACZ,YAAY,GAAG,EAAE,CAAC,EAAE,UAAU,IAAI,EAAE,CAAC,EAAE,WAAW;AAAA,EACpD;AACF;AACA,IAAM,KAAK;AAAX,IAAgB,KAAK,KAAK,IAAI,KAAK,KAAK,MAAM,EAAE,GAAG,CAAC;AAApD,IAAuD,KAAK;AAA5D,IAAkE,KAAK;AACvE,SAAS,KAAK;AACZ,MAAI,IAAI,GAAG,GAAG,GAAG,IAAI;AACrB,SAAO,CAAC,MAAM;AACZ,eAAW,KAAK,EAAE,OAAO;AACvB,UAAI,KAAK,EAAE,SAAS,iBAAiB,EAAE,SAAS,SAAS;AACvD,cAAM,IAAI,EAAE,mBAAmB,IAAI,EAAE;AACrC,YAAI,KAAK,IAAI,GAAG;AACd,gBAAM,IAAI,IAAI,GAAG,IAAI,IAAI;AACzB,cAAI,IAAI;AAAA,QACV;AACA,YAAI,GAAG,IAAI;AACX,cAAM,IAAI,EAAE,eAAe,IAAI,IAAI,IAAI;AACvC,eAAO,IAAI,GAAG;AAAA,UACZ,aAAa;AAAA,UACb,0BAA0B;AAAA,UAC1B,aAAa,EAAE;AAAA,QACjB;AAAA,MACF;AACF,WAAO;AAAA,MACL,aAAa;AAAA,MACb,0BAA0B;AAAA,IAC5B;AAAA,EACF;AACF;AACA,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,OAAI;AACjC,MAAI,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,OAAI,IAAI,EAAE,SAAS,IAAI,EAAE,SAAS,IAAI,GAAG,IAAI;AACvE,QAAM,IAAI,GAAG;AACb,SAAO,YAAY,YAAY;AAC7B,UAAM,IAAI,MAAM,EAAE,SAAS,GAAG;AAAA,MAC5B,aAAa;AAAA,MACb,0BAA0B;AAAA,MAC1B,aAAa;AAAA,IACf,IAAI,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC;AAClB,QAAI;AACF,UAAI,GAAG,IAAI,IAAI,GAAG,IAAI,IAAI,KAAK,EAAE,SAAS,IAAI,MAAM,IAAI,IAAI,EAAE,OAAO,GAAG,MAAM,MAAM,KAAK,QAAQ,EAAE,CAAC,GAAG,IAAI,GAAG,KAAK,GAAG,IAAI,IAAI,MAAM,KAAK,QAAQ,EAAE,EAAE,KAAK,GAAG,IAAI,EAAE,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,OAAK,EAAE,KAAK,CAAC;AAAA,aACpM,MAAM,KAAK,KAAK,KAAK;AAC5B,YAAM,IAAI,GAAG,GAAG,IAAI,CAAC;AACrB,WAAK,QAAQ,EAAE,EAAE,MAAM,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,IAAI,GAAG,IAAI;AAAA,IACpD;AAAA,EACF,GAAG,EAAE;AACP;AACA,IAAI,KAAK;AACT,IAAM,IAAI,CAAC,GAAG,MAAM,MAAM,QAAQ,IAAI,GAAG,CAAC;AAA1C,IAA6C,MAAM,OAAO,qBAAqB,OAAO,2BAA2B,OAAO,sBAAsB,KAAK,MAAM;AACzJ,SAAS,GAAG,GAAG;AACb,UAAQ,GAAG;AAAA,IACT,KAAK;AACH,aAAO,EAAE;AAAA,IACX,KAAK;AACH,aAAO,EAAE;AAAA,IACX,KAAK;AACH,aAAO,EAAE;AAAA,IACX,KAAK;AACH,aAAO,EAAE;AAAA,IACX,KAAK;AACH,aAAO,EAAE;AAAA,IACX,KAAK;AACH,aAAO,EAAE;AAAA,IACX,KAAK;AACH,aAAO,EAAE;AAAA,IACX;AACE,aAAO,EAAE;AAAA,EACb;AACF;AACA,SAAS,GAAG,GAAG;AACb,QAAM,CAAC,GAAG,IAAI,EAAE,IAAI,EAAE,MAAM,OAAO;AACnC,MAAI;AACF,UAAM,IAAI,KAAK,MAAM,CAAC;AACtB,WAAO,EAAE,+BAA+B;AAAA,MACtC,SAAS;AAAA,MACT,MAAM;AAAA,IACR,CAAC,GAAG;AAAA,MACF,SAAS;AAAA,MACT,MAAM;AAAA,IACR;AAAA,EACF,SAAS,GAAG;AACV,WAAO,EAAE,kEAAkE;AAAA,MACzE,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT,CAAC,GAAG;AAAA,MACF,SAAS;AAAA,MACT,MAAM;AAAA,IACR;AAAA,EACF;AACF;AACA,SAAS,GAAG;AAAA,EACV,aAAa;AAAA,EACb,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,QAAQ;AACV,GAAG;AACD,QAAM,EAAE,SAAS,MAAM,EAAE,QAAQ,KAAK,QAAQ,EAAE,EAAE,KAAK,IAAI,MAAM,EAAE,QAAQ,MAAM,EAAE,SAAS,KAAK,QAAQ,EAAE,EAAE,MAAM,CAAC;AACtH;AACA,SAAS,GAAG;AAAA,EACV,aAAa;AAAA,EACb,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,QAAQ;AACV,GAAG;AACD,QAAM,EAAE,QAAQ,KAAK,QAAQ,EAAE,EAAE,KAAK,IAAI,MAAM,EAAE,SAAS,KAAK,QAAQ,EAAE,EAAE,MAAM,CAAC,IAAI,MAAM,EAAE,QAAQ,KAAK,QAAQ,EAAE,EAAE,OAAO,IAAI,MAAM,EAAE,SAAS,KAAK,QAAQ,EAAE,EAAE,IAAI;AAC3K;AACA,SAAS,GAAG;AAAA,EACV,aAAa;AAAA,EACb,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,YAAY;AAAA,EACZ,QAAQ;AACV,GAAG;AACD,QAAM,EAAE,SAAS,GAAG;AAAA,IAClB,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,QAAQ;AAAA,EACV,CAAC,IAAI,MAAM,EAAE,UAAU,GAAG;AAAA,IACxB,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,QAAQ;AAAA,EACV,CAAC;AACH;AACA,eAAe,GAAG,GAAG,GAAG;AAAA,EACtB,OAAO,IAAI;AAAA,EACX,WAAW;AAAA,EACX,MAAM;AAAA,EACN,SAAS,IAAI;AAAA,EACb,WAAW;AACb,GAAG;AACD,MAAI;AACJ,OAAK;AACL,MAAI,IAAI,OAAI,IAAI,OAAI,IAAI,EAAE,MAAM,IAAI,EAAE;AACtC,QAAM;AAAA,IACJ,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,OAAO;AAAA,IACP,cAAc;AAAA,IACd,iBAAiB;AAAA,EACnB,IAAI,GAAG,GAAG,GAAG,GAAG,EAAE,OAAO,GAAG;AAAA,IAC1B,IAAI;AAAA,IACJ,OAAO;AAAA,IACP,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,mBAAmB;AAAA,EACrB,IAAI,MAAM,EAAE,CAAC;AACb,GAAC,IAAI,EAAE,oBAAoB,QAAQ,EAAE,KAAK,GAAG;AAAA,IAC3C,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,UAAU;AAAA,EACZ,CAAC;AACD,QAAM,IAAI,IAAI,GAAG;AAAA,IACf,YAAY;AAAA,EACd,CAAC,GAAG,IAAI,EAAE,kBAAkB,kBAAkB;AAC9C,MAAI,CAAC;AACH,UAAM,IAAI,MAAM,6BAA6B;AAC/C,QAAM,IAAI,IAAI,EAAE,SAAS,EAAE;AAC3B,IAAE,OAAO;AAAA,IACP,eAAe;AAAA,EACjB,CAAC;AACD,QAAM,IAAI,EAAE,iBAAiB,CAAC,GAAG,IAAI,MAAM,GAAG,IAAI,MAAM;AACtD,QAAI;AACJ,QAAI,MAAI,OAAO,IAAI,EAAE,4BAA4B,QAAQ,EAAE,KAAK,GAAG,EAAE,SAAS;AAAA,EAChF,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,MAAM,GAAG;AAAA,IAC9B,aAAa,IAAI;AAAA,IACjB,mBAAmB,MAAM,EAAE,SAAS,IAAI;AAAA,IACxC,oBAAoB,EAAE;AAAA,IACtB,4BAA4B,EAAE;AAAA,IAC9B,QAAQ;AAAA,IACR,YAAY;AAAA,EACd,CAAC,GAAG,CAAC,MAAM;AACT,QAAI;AACJ,YAAQ,IAAI,EAAE,8BAA8B,OAAO,SAAS,EAAE,KAAK,GAAG,CAAC;AAAA,EACzE,GAAG,CAAC;AACJ,IAAE,iBAAiB,CAAC,MAAM;AACxB,QAAI;AACJ,MAAE,iCAAiC,CAAC;AACpC,QAAI;AACF,QAAE,aAAa,EAAE,UAAU,UAAU,EAAE,UAAU,kBAAkB,OAAO,EAAE,GAAG;AAAA,QAC7E,WAAW,EAAE,UAAU;AAAA,QACvB,QAAQ,EAAE,UAAU;AAAA,QACpB,eAAe,EAAE,UAAU;AAAA,MAC7B,GAAG,CAAC,IAAI,EAAE,GAAG;AAAA,QACX,WAAW;AAAA,MACb,GAAG,CAAC;AAAA,IACN,SAAS,GAAG;AACV,OAAC,IAAI,EAAE,YAAY,QAAQ,EAAE,KAAK,GAAG,GAAG;AAAA,QACtC,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAAA,EACF,GAAG,EAAE,SAAS,MAAM;AAClB,QAAI,OAAK,CAAC,KAAK,MAAM,EAAE;AAAA,EACzB;AACA,QAAM,IAAI,CAAC,MAAM;AACf,QAAI;AACJ,KAAC,IAAI,EAAE,oBAAoB,QAAQ,EAAE,KAAK,GAAG,CAAC;AAAA,EAChD;AACA,WAAS,EAAE,GAAG,GAAG;AACf,QAAI,MAAM,EAAE,iBAAiB,OAAO,KAAK,YAAY,cAAc,GAAG;AACpE,YAAM,IAAI,EAAE;AACZ,QAAE,EAAE,OAAO;AAAA,IACb;AACA,UAAM,EAAE,cAAc,EAAE,IAAI,GAAG,IAAI,MAAM,EAAE,gBAAgB,EAAE,QAAQ,EAAE,MAAM,GAAG;AAAA,MAC9E,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,MAClC,mBAAmB;AAAA,MACnB,oBAAoB,EAAE;AAAA,MACtB,4BAA4B,EAAE;AAAA,MAC9B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACA,WAAS,EAAE,GAAG,GAAG;AACf,UAAM,IAAI,OAAO,KAAK,WAAW,IAAI,KAAK,OAAO,SAAS,EAAE;AAC5D,SAAK,EAAE,OAAO;AAAA,MACZ,gBAAgB;AAAA,IAClB,CAAC,GAAG,EAAE,MAAM,cAAc;AAAA,MACxB,OAAO;AAAA,IACT,CAAC;AAAA,EACH;AACA,QAAM,IAAI;AAAA,IACR,CAAC,EAAE,aAAa,GAAG;AAAA,IACnB,CAAC,EAAE,UAAU,GAAG;AAAA,IAChB,CAAC,EAAE,WAAW,GAAG;AAAA,EACnB;AACA,IAAE,YAAY,CAAC,MAAM;AACnB,QAAI;AACJ,UAAM;AAAA,MACJ,SAAS;AAAA,MACT,MAAM;AAAA,IACR,IAAI,GAAG,EAAE,IAAI;AACb,KAAC,IAAI,EAAE,CAAC,MAAM,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC;AAAA,EACtC,GAAG,EAAE,6BAA6B,MAAM;AACtC,QAAI;AACJ,MAAE,kDAAkD,EAAE,kBAAkB;AACxE,UAAM,IAAI,GAAG,EAAE,kBAAkB;AACjC,UAAM,EAAE,eAAe,IAAI,EAAE,4BAA4B,QAAQ,EAAE,KAAK,GAAG,CAAC;AAAA,EAC9E,GAAG,EAAE,UAAU,CAAC,MAAM;AACpB,QAAI;AACJ,MAAE,0BAA0B,CAAC,IAAI,IAAI,EAAE,qBAAqB,QAAQ,EAAE,KAAK,GAAG,EAAE,QAAQ,CAAC,CAAC;AAAA,EAC5F,GAAG,MAAM,EAAE,qBAAqB,CAAC,GAAG,EAAE,2BAA2B;AACjE,QAAM,IAAI,MAAM,EAAE,aAAa;AAC/B,SAAO,EAAE,kBAAkB,GAAG,MAAM,EAAE,oBAAoB,CAAC,GAAG,EAAE,0BAA0B,GAAG,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,EAAE,qBAAqB,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA,IAKvI,MAAM,GAAG;AACP,aAAO,EAAE,GAAG,GAAG,CAAC;AAAA,IAClB;AAAA;AAAA;AAAA;AAAA,IAIA,MAAM,aAAa;AACjB,UAAI;AACJ,UAAI,GAAG;AACL,cAAM,IAAI,GAAG,EAAE,kBAAkB;AACjC,YAAI,GAAG;AACL,cAAI,MAAM,EAAE,KAAK;AACf,0BAAc,CAAC;AACf;AAAA,UACF;AACA,YAAE,MAAM,GAAG,EAAE,6BAA6B,MAAM,EAAE,sBAAsB,MAAM,EAAE,iBAAiB,MAAM,EAAE,UAAU;AAAA,QACrH;AACA,YAAI;AACF,gBAAM,EAAE,aAAa,MAAM,EAAE,GAAG,CAAC,EAAE,MAAM,CAAC,MAAM;AAAA,UAChD,CAAC;AAAA,QACH,SAAS,GAAG;AACV,YAAE,oCAAoC,CAAC;AAAA,QACzC;AACA,SAAC,IAAI,EAAE,+BAA+B,QAAQ,EAAE,KAAK,GAAG,EAAE,IAAI,GAAG,cAAc,CAAC;AAAA,MAClF;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAIA,uBAAuB,GAAG;AACxB,UAAI,GAAG;AACP,UAAI,CAAC,KAAK,EAAE,eAAe,QAAQ;AACjC,UAAE,gDAAgD,IAAI,IAAI,EAAE,YAAY,QAAQ,EAAE,KAAK,GAAG,IAAI,MAAM,gDAAgD,GAAG;AAAA,UACrJ,UAAU;AAAA,QACZ,CAAC;AACD;AAAA,MACF;AACA,UAAI;AACF,UAAE,KAAK,CAAC;AAAA,MACV,SAAS,GAAG;AACV,UAAE,sCAAsC,CAAC,IAAI,IAAI,EAAE,YAAY,QAAQ,EAAE,KAAK,GAAG,GAAG;AAAA,UAClF,UAAU;AAAA,QACZ,CAAC;AAAA,MACH;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAIA,WAAW;AAAA;AAAA;AAAA;AAAA,IAIX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,oBAAoB;AAAA,EACtB;AACF;AACA,SAAS,GAAG,GAAG;AACb,QAAM;AAAA,IACJ,eAAe;AAAA,EACjB,IAAI,KAAK,CAAC;AACV,SAAO;AAAA,IACL,mBAAmB,KAAK,OAAO,SAAS,EAAE;AAAA,IAC1C,iBAAiB,KAAK,OAAO,SAAS,EAAE;AAAA,IACxC,eAAe,KAAK,OAAO,SAAS,EAAE;AAAA,IACtC,oBAAoB,KAAK,OAAO,SAAS,EAAE;AAAA,IAC3C,QAAQ,KAAK,OAAO,SAAS,EAAE;AAAA,EACjC;AACF;AACA,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AACzB,QAAM,EAAE,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACvD;AACA,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AACzB,QAAM,EAAE,QAAQ,EAAE,MAAM,kBAAkB;AAAA,IACxC,OAAO;AAAA,IACP,eAAe;AAAA,EACjB,CAAC,IAAI,MAAM,EAAE,QAAQ,EAAE,MAAM,kBAAkB;AAAA,IAC7C,OAAO;AAAA,IACP,gBAAgB,EAAE,UAAU,SAAS,UAAU,EAAE,UAAU;AAAA,IAC3D,YAAY,EAAE,UAAU,SAAS,UAAU,EAAE,UAAU;AAAA,IACvD,eAAe;AAAA,IACf,GAAG;AAAA,EACL,CAAC;AACH;AACA,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG;AACtB,IAAE,IAAI,KAAK,MAAM,MAAM,EAAE,QAAQ,EAAE,UAAU,eAAe;AAAA,IAC1D,OAAO;AAAA,IACP,SAAS,EAAE,IAAI,IAAE;AAAA,IACjB,eAAe;AAAA,EACjB,GAAG,SAAS,CAAC,EAAE,kBAAkB,CAAC,IAAI,MAAM,EAAE,QAAQ,EAAE,UAAU,eAAe;AAAA,IAC/E,OAAO;AAAA,IACP,gBAAgB,EAAE,UAAU,SAAS,UAAU,EAAE,UAAU;AAAA,IAC3D,YAAY,EAAE,UAAU,SAAS,UAAU,EAAE,UAAU;AAAA,IACvD,eAAe;AAAA,EACjB,GAAG,QAAQ,CAAC,EAAE,eAAe,CAAC;AAChC;AACA,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AACzB,IAAE,IAAI,KAAK,MAAM,MAAM,EAAE,QAAQ,EAAE,UAAU,eAAe;AAAA,IAC1D,OAAO;AAAA,IACP,SAAS,EAAE,IAAI,IAAE;AAAA,IACjB,eAAe;AAAA,EACjB,GAAG,SAAS,CAAC,EAAE,kBAAkB,CAAC,IAAI,MAAM,EAAE,QAAQ,EAAE,UAAU,eAAe;AAAA,IAC/E,OAAO;AAAA,IACP,gBAAgB,EAAE,UAAU,SAAS,UAAU,EAAE,UAAU;AAAA,IAC3D,YAAY,EAAE,UAAU,SAAS,UAAU,EAAE,UAAU;AAAA,IACvD,eAAe;AAAA,IACf,GAAG;AAAA,EACL,GAAG,QAAQ,CAAC,EAAE,eAAe,CAAC;AAChC;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,SAAO,EAAE,MAAM,GAAG,IAAI,QAAQ,OAAO,GAAG,MAAM;AAC5C,QAAI;AACF,YAAM,IAAI,MAAM,GAAG,EAAE,IAAI,GAAG,CAAC,GAAG;AAAA,QAC9B,GAAG;AAAA,QACH,WAAW;AAAA,QACX,WAAW;AAAA,UACT,GAAG,EAAE;AAAA,UACL,yBAAyB,CAAC,MAAM;AAC9B,gBAAI,GAAG;AACP,aAAC,KAAK,IAAI,EAAE,WAAW,4BAA4B,QAAQ,EAAE,KAAK,GAAG,CAAC,GAAG,MAAM,EAAE,aAAa,EAAE,CAAC;AAAA,UACnG;AAAA,UACA,oBAAoB,CAAC,GAAG,MAAM;AAC5B,gBAAI,GAAG;AACP,aAAC,KAAK,IAAI,EAAE,WAAW,uBAAuB,QAAQ,EAAE,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,UAAU;AAAA,UACjG;AAAA,UACA,4BAA4B,CAAC,MAAM;AACjC,gBAAI,GAAG;AACP,aAAC,KAAK,IAAI,EAAE,WAAW,+BAA+B,QAAQ,EAAE,KAAK,GAAG,CAAC,GAAG,MAAM,EAAE,UAAU,GAAG,OAAO,IAAI,GAAG,MAAM,GAAG,GAAG,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,GAAG,GAAG,EAAE,UAAU;AAAA,UACnL;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,SAAS,GAAG;AACV,QAAE,CAAC;AAAA,IACL;AAAA,EACF,CAAC;AACH;AACA,eAAe,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAC/B,MAAI,GAAG,GAAG,GAAG;AACb,QAAM,IAAI,GAAG,GAAG,GAAG,GAAG,EAAE,MAAM,EAAE,gBAAgB,CAAC,GAAG,IAAI,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,MAAM,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG;AAAA,IACvG,MAAM;AAAA,IACN,UAAU;AAAA,EACZ,IAAI;AACJ,SAAO,KAAK,MAAM,EAAE,SAAS,EAAE,OAAO,IAAI,KAAK,IAAI,EAAE,WAAW,iBAAiB,QAAQ,EAAE,KAAK,GAAG,CAAC,GAAG,MAAM,EAAE,gBAAgB,KAAK,IAAI,EAAE,WAAW,YAAY,QAAQ,EAAE,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,QAAQ,EAAE,WAAW,GAAG;AAAA,IAC1N,MAAM;AAAA,EACR,KAAK;AAAA,IACH,MAAM;AAAA,IACN,kBAAkB;AAAA,EACpB;AACF;AACA,eAAe,GAAG,GAAG,GAAG;AACtB,MAAI,GAAG,GAAG;AACV,MAAI,IAAI,MAAI,IAAI;AAChB,QAAM,IAAI,EAAE,eAAe,IAAI,IAAI,EAAE,SAAS,IAAI,IAAI,EAAE,WAAW,GAAG,IAAI;AAAA,IACxE,UAAU,CAAC;AAAA,IACX,UAAU,EAAE,QAAQ,EAAE;AAAA,EACxB,GAAG,IAAI,GAAG;AAAA,IACR,OAAO;AAAA,IACP,SAAS;AAAA,IACT,WAAW,EAAE;AAAA,IACb,YAAY,EAAE;AAAA,EAChB,CAAC;AACD,IAAE,MAAM,aAAa;AAAA,IACnB,OAAO;AAAA,EACT,CAAC;AACD,QAAM,IAAI,GAAG,EAAE,MAAM,GAAG,EAAE,UAAU,OAAO,GAAG,IAAI,MAAM,EAAE,QAAQ,CAAC;AACnE,IAAE,OAAO,GAAG,CAAC,CAAC;AACd,QAAM;AAAA,IACJ,WAAW;AAAA,IACX,YAAY;AAAA,EACd,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,MAAM;AACvB,QAAI;AACJ,YAAQ,IAAI,EAAE,kBAAkB,OAAO,SAAS,EAAE,WAAW;AAAA,EAC/D,CAAC;AACD,IAAE,WAAW,GAAG,EAAE,eAAe,IAAI,KAAK,IAAI,EAAE,WAAW,iBAAiB,QAAQ,EAAE,KAAK,GAAG,CAAC,GAAG,EAAE,QAAQ,GAAG,QAAQ;AACvH,QAAM,IAAI,CAAC,MAAM;AACf,QAAI;AAAA,EACN;AACA,IAAE,MAAM,aAAa;AAAA,IACnB,OAAO;AAAA,IACP,GAAG,GAAG,CAAC;AAAA,EACT,CAAC;AACD,iBAAe,EAAE,GAAG;AAClB,QAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AACtB,KAAC,KAAK,IAAI,EAAE,WAAW,4BAA4B,QAAQ,EAAE,KAAK,GAAG,EAAE,UAAU,GAAG,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,OAAO,EAAE,OAAO,KAAK,IAAI,EAAE,WAAW,iBAAiB,QAAQ,EAAE,KAAK,GAAG,CAAC,GAAG,EAAE,QAAQ,GAAG,QAAQ;AAC7M,UAAM,IAAI,EAAE,SAAS,EAAE,iBAAiB,QAAQ,QAAQ,MAAM,IAAI,GAAG,EAAE,MAAM,GAAG;AAAA,MAC9E,WAAW;AAAA,MACX,SAAS,EAAE,UAAU;AAAA,IACvB,CAAC,GAAG,IAAI,GAAG,MAAM,GAAG,GAAG;AAAA,MACrB,GAAG;AAAA,MACH,WAAW;AAAA,QACT,GAAG,EAAE;AAAA,QACL,iBAAiB;AAAA,MACnB;AAAA,IACF,GAAG,GAAG,GAAG,EAAE,IAAI,GAAG;AAAA,MAChB,OAAO;AAAA,MACP,SAAS;AAAA,MACT,qBAAqB;AAAA;AAAA,MAErB,eAAe,CAAC,OAAO,KAAK,OAAO,SAAS,EAAE,aAAa,uBAAuB,EAAE,WAAW;AAAA,MAC/F,SAAS;AAAA,IACX,CAAC,EAAE,MAAM,CAAC,MAAM;AACd,UAAI,GAAG;AACP,YAAM,EAAE,EAAE,WAAW,IAAI,KAAK,IAAI,EAAE,WAAW,4BAA4B,QAAQ,EAAE,KAAK,GAAG,EAAE,IAAI,GAAG;AAAA,IACxG,CAAC,GAAG,CAAC,GAAG;AAAA,MACN,kBAAkB;AAAA,MAClB,MAAM;AAAA,IACR,CAAC,IAAI,MAAM,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7B,SAAK,EAAE,SAAS,IAAI,EAAE,SAAS,OAAO,SAAS,EAAE,SAAS,KAAK,IAAI,EAAE,WAAW,cAAc,QAAQ,EAAE,KAAK,GAAG,EAAE,EAAE,IAAI,EAAE,mBAAmB,GAAG,EAAE,gBAAgB,GAAG,EAAE,OAAO,GAAG,IAAI,OAAI,EAAE,OAAO;AAAA,MAChM,QAAQ,KAAK,OAAO,SAAS,EAAE;AAAA,MAC/B,UAAU,KAAK,OAAO,SAAS,EAAE;AAAA,MACjC,MAAM,EAAE;AAAA,IACV,CAAC,GAAG,GAAG,KAAK,OAAO,SAAS,EAAE,cAAc,EAAE,QAAQ,EAAE,UAAU;AAAA,EACpE;AACA,iBAAe,IAAI;AACjB,QAAI,GAAG,GAAG,GAAG;AACb,KAAC,IAAI,EAAE,kBAAkB,QAAQ,EAAE,WAAW,GAAG,QAAQ,IAAI,EAAE,qBAAqB,OAAO,SAAS,EAAE,WAAW,IAAI,OAAO,EAAE,kBAAkB,OAAO,EAAE,gBAAgB,KAAK,IAAI,EAAE,WAAW,4BAA4B,QAAQ,EAAE,KAAK,GAAG,EAAE,YAAY;AAAA,EAC7P;AACA,iBAAe,EAAE,GAAG;AAClB,QAAI,GAAG;AACP,UAAM,EAAE,aAAa,EAAE,MAAM,qBAAqB;AAAA,MAChD,MAAM;AAAA,IACR,CAAC,GAAG,EAAE,WAAW,GAAG,EAAE,aAAa,EAAE,cAAc,MAAM,EAAE,IAAI,KAAK,IAAI,EAAE,WAAW,iBAAiB,QAAQ,EAAE,KAAK,GAAG,CAAC;AAAA,EAC3H;AACA,SAAO;AAAA,IACL,OAAO;AAAA,IACP,eAAe,MAAM;AACnB,UAAI;AACJ,cAAQ,IAAI,EAAE,qBAAqB,OAAO,SAAS,EAAE;AAAA,IACvD;AAAA,IACA,yBAAyB,MAAM;AAC7B,UAAI;AACJ,eAAS,IAAI,EAAE,qBAAqB,OAAO,SAAS,EAAE,uBAAuB;AAAA,IAC/E;AAAA,IACA,mBAAmB,IAAI,EAAE,cAAc,OAAO,SAAS,EAAE,oBAAoB,CAAC;AAAA,IAC9E,aAAa,MAAM,EAAE,YAAY,EAAE,EAAE;AAAA,IACrC,YAAY;AAAA,IACZ,iBAAiB,EAAE;AAAA,IACnB,MAAM,UAAU;AACd,YAAM,EAAE,IAAE,GAAG,EAAE,MAAM,cAAc;AAAA,QACjC,OAAO;AAAA,QACP,MAAM,EAAE;AAAA,MACV,CAAC;AAAA,IACH;AAAA,IACA,MAAM,YAAY;AAChB,YAAM,EAAE,GAAG,MAAM,EAAE,KAAE,GAAG,EAAE,MAAM,cAAc;AAAA,QAC5C,OAAO;AAAA,QACP,MAAM,EAAE;AAAA,MACV,CAAC;AAAA,IACH;AAAA,IACA,MAAM,aAAa;AACjB,YAAM,EAAE,GAAG,EAAE,MAAM,cAAc;AAAA,QAC/B,OAAO;AAAA,QACP,MAAM,EAAE;AAAA,MACV,CAAC;AAAA,IACH;AAAA,IACA,MAAM,KAAK,GAAG;AACZ,UAAI,GAAG,GAAG,GAAG,GAAG;AAChB,YAAM,IAAI,MAAM;AACd,YAAI,GAAG,EAAE,IAAI;AACX,gBAAM,IAAI,EAAE,GAAG,EAAE,IAAI,+BAA+B;AACtD,YAAI,EAAE,UAAU;AACd,gBAAM,IAAI,EAAE,4CAA4C;AAC1D,YAAI,EAAE,WAAW;AACf,gBAAM,IAAI,EAAE,yBAAyB;AACvC,YAAI,EAAE,aAAa,EAAE;AACnB,gBAAM,IAAI,EAAE,6BAA6B;AAC3C,YAAI,CAAC,CAAC,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,GAAG;AACpD,cAAI,CAAC,EAAE;AACL,kBAAM,IAAI,EAAE,sCAAsC;AACpD,cAAI,CAAC,EAAE;AACL,kBAAM,IAAI,EAAE,yBAAyB;AAAA,QACzC;AAAA,MACF,GAAG,IAAI,YAAY;AACjB,YAAI,GAAG;AACP,YAAI,CAAC,EAAE,MAAM;AACX,gBAAM,IAAI,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE,UAAU,EAAE,cAAc;AACxD,cAAI,CAAC,EAAE;AACL,kBAAM,IAAI,GAAG,EAAE,UAAU,CAAC,CAAC,EAAE,cAAc;AAC7C,YAAE,OAAO,EAAE,OAAO,KAAK,IAAI,EAAE,WAAW,cAAc,QAAQ,EAAE,KAAK,GAAG,EAAE,KAAK,EAAE;AAAA,QACnF;AACA,eAAO,EAAE,KAAK;AAAA,MAChB,GAAG,IAAI,OAAO,GAAG,MAAM,GAAG,MAAM;AAC9B,YAAI,GAAG;AACP,eAAO,EAAE,KAAK,EAAE,IAAI,GAAG;AAAA,UACrB,UAAU,EAAE;AAAA,UACZ,WAAW,IAAI,EAAE,qBAAqB,OAAO,SAAS,EAAE;AAAA,UACxD,YAAY,IAAI,EAAE,qBAAqB,OAAO,SAAS,EAAE;AAAA,UACzD,UAAU,EAAE,IAAI,CAAC;AAAA,YACf,SAAS;AAAA,YACT,GAAG;AAAA,UACL,MAAM,CAAC;AAAA,QACT,GAAG;AAAA,UACD,GAAG,GAAG,EAAE,QAAQ;AAAA,UAChB,kBAAkB;AAAA,QACpB,CAAC;AAAA,MACH,GAAG;AAAA,QACD,OAAO;AAAA,QACP,eAAe,CAAC,MAAM;AACpB,cAAI,GAAG,GAAG,GAAG;AACb,gBAAM,KAAK,IAAI,KAAK,OAAO,SAAS,EAAE,YAAY,OAAO,SAAS,EAAE,SAAS,+BAA+B;AAC5G,iBAAO,GAAG,IAAI,KAAK,OAAO,SAAS,EAAE,YAAY,OAAO,SAAS,EAAE,SAAS,cAAc,MAAM,CAAC,MAAM,KAAK,IAAI,EAAE,WAAW,YAAY,QAAQ,EAAE,KAAK,GAAG,CAAC,GAAG,SAAM;AAAA,QACvK;AAAA,QACA,SAAS,YAAY;AACnB,gBAAM,EAAE,GAAG,MAAM,EAAE,KAAE;AAAA,QACvB;AAAA,MACF,CAAC;AACD,UAAI;AACF,UAAE,GAAG,EAAE,GAAG,EAAE,SAAS,KAAK;AAAA,UACxB,IAAI,EAAE;AAAA,UACN,MAAM;AAAA,UACN,SAAS;AAAA,UACT,YAAY,IAAI,KAAK,EAAE,OAAO,CAAC,EAAE,YAAY;AAAA,QAC/C,CAAC,IAAI,KAAK,IAAI,EAAE,WAAW,iBAAiB,QAAQ,EAAE,KAAK,GAAG,CAAC,GAAG,EAAE,QAAQ,GAAG,MAAM;AACrF,cAAM,IAAI,MAAM,EAAE,GAAG,IAAI,MAAM,EAAE,CAAC,GAAG,EAAE,QAAQ,GAAG,CAAC;AACnD,eAAO,EAAE,SAAS,KAAK;AAAA,UACrB,IAAI,EAAE;AAAA,UACN,MAAM;AAAA,UACN,SAAS,EAAE,UAAU;AAAA,UACrB,aAA6B,oBAAI,KAAK,GAAG,YAAY;AAAA,UACrD,SAAS,EAAE;AAAA,UACX,SAAS,EAAE;AAAA,QACb,CAAC,GAAG,EAAE,MAAM,sBAAsB;AAAA,UAChC,OAAO;AAAA,UACP,UAAU,EAAE,SAAS,SAAS;AAAA,QAChC,CAAC,GAAG,EAAE,YAAY,KAAK,IAAI,EAAE,WAAW,iBAAiB,QAAQ,EAAE,KAAK,GAAG,CAAC,GAAG,EAAE,QAAQ,GAAG,QAAQ,GAAG,EAAE,MAAM,0BAA0B;AAAA,UACvI,SAAS,EAAE,IAAI,IAAE;AAAA,UACjB,UAAU,EAAE,SAAS;AAAA,QACvB,CAAC,IAAI;AAAA,MACP,SAAS,GAAG;AACV,gBAAQ,IAAI,EAAE,SAAS,EAAE,SAAS,SAAS,CAAC,MAAM,OAAO,SAAS,EAAE,UAAU,eAAe,EAAE,SAAS,IAAI,GAAG,EAAE,MAAM,sBAAsB;AAAA,UAC3I,OAAO;AAAA,UACP,UAAU,EAAE,SAAS;AAAA,QACvB,CAAC,GAAG;AAAA,MACN;AAAA,IACF;AAAA,IACA,KAAK,GAAG,GAAG,GAAG;AACZ,UAAI,GAAG,GAAG,GAAG;AACb,YAAM,IAAI,EAAE,SAAS,KAAK,CAAC,MAAM,EAAE,OAAO,CAAC;AAC3C,UAAI,EAAE,MAAM;AACV,YAAI,CAAC;AACH,gBAAM,IAAI,MAAM,mBAAmB;AAAA,MACvC;AACE,cAAM,IAAI,MAAM,yBAAyB;AAC3C,YAAM,MAAM,IAAI,EAAE,YAAY,OAAO,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,aAAa,EAAE,EAAE,CAAC,MAAM,CAAC;AACvF,aAAO,EAAE,MAAM,cAAc;AAAA,QAC3B,OAAO,IAAI,WAAW;AAAA,QACtB,OAAO,MAAM,IAAI,OAAO;AAAA,QACxB,gBAAgB,IAAI,EAAE,cAAc,OAAO,SAAS,EAAE,OAAO;AAAA,QAC7D,SAAS;AAAA,QACT,OAAO;AAAA,MACT,CAAC,GAAG,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,KAAK,IAAI,GAAG;AAAA,QACzC,gBAAgB,IAAI,EAAE,cAAc,OAAO,SAAS,EAAE,OAAO;AAAA,QAC7D,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,OAAO;AAAA,MACT,CAAC,IAAI,EAAE,aAAa,EAAE,IAAI,EAAE,KAAK,IAAI;AAAA,QACnC,gBAAgB,IAAI,EAAE,cAAc,OAAO,SAAS,EAAE,OAAO;AAAA,QAC7D,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,OAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,IACA,WAAW,GAAG;AACZ,UAAI,CAAC,EAAE;AACL,cAAM,IAAI,MAAM,yBAAyB;AAC3C,aAAO,EAAE,MAAM,qBAAqB;AAAA,QAClC,MAAM;AAAA,MACR,CAAC,GAAG,EAAE,aAAa,EAAE,IAAI,EAAE,KAAK,IAAI,CAAC;AAAA,IACvC;AAAA,IACA,MAAM,MAAM,GAAG;AACb,UAAI,GAAG,GAAG;AACV,eAAS,IAAI;AACX,YAAI,OAAO,KAAK,UAAU;AACxB,cAAI,CAAC,EAAE,UAAU;AACf,kBAAM,IAAI,MAAM,oCAAoC;AACtD,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,UAAU,EAAE,UAAU;AAAA,YACtB,OAAO;AAAA,YACP,MAAM;AAAA,UACR;AAAA,QACF;AACA,YAAI,EAAE,SAAS,UAAU,CAAC,EAAE,UAAU;AACpC,cAAI,CAAC,EAAE,UAAU;AACf,kBAAM,IAAI,MAAM,oCAAoC;AACtD,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,UAAU,EAAE,UAAU;AAAA,YACtB,OAAO,EAAE;AAAA,YACT,MAAM,EAAE;AAAA,UACV;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,YAAM,IAAI,EAAE;AACZ,UAAI,EAAE,MAAM,eAAe,CAAC,GAAG,EAAE,OAAO,GAAG,EAAE,YAAY,EAAE,SAAS,WAAW,EAAE,SAAS,KAAK;AAAA,QAC7F,IAAI,EAAE;AAAA,QACN,MAAM;AAAA,QACN,SAAS,EAAE;AAAA,QACX,YAAY,IAAI,KAAK,EAAE,IAAI,IAAE,CAAC,EAAE,YAAY;AAAA,MAC9C,CAAC,IAAI,KAAK,IAAI,EAAE,WAAW,iBAAiB,QAAQ,EAAE,KAAK,GAAG,CAAC,GAAG,EAAE,QAAQ,GAAG,QAAQ,IAAI,GAAG,EAAE,QAAQ;AACtG,eAAO;AAAA,UACL,UAAU;AAAA,UACV,UAAU;AAAA,UACV,QAAQ;AAAA,QACV;AACF,UAAI,CAAC,EAAE;AACL,cAAM,IAAI,MAAM,mCAAmC;AACrD,aAAO,EAAE,iBAAiB,MAAM;AAAA,QAC9B,QAAQ;AAAA,QACR,UAAU;AAAA,UACR,UAAU,IAAI,EAAE,SAAS,OAAO,SAAS,EAAE;AAAA,UAC3C,UAAU,EAAE;AAAA,QACd;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,MAAM,UAAU;AAAA,MACd,MAAM;AAAA,IACR,GAAG;AACD,UAAI,GAAG,GAAG;AACV,SAAG,EAAE,mBAAmB,IAAI,EAAE,qBAAqB,OAAO,SAAS,EAAE,YAAY,CAAC;AAClF,YAAM,IAAI,EAAE,SAAS,EAAE,SAAS,SAAS,CAAC;AAC1C,QAAE,MAAM,yBAAyB;AAAA,QAC/B,MAAM,KAAK;AAAA,QACX,6BAA6B,GAAG,IAAI,IAAE;AAAA,QACtC,+BAA+B,EAAE,IAAI,IAAE;AAAA,MACzC,CAAC,GAAG,EAAE,cAAc,OAAK,KAAK,IAAI,EAAE,WAAW,iBAAiB,QAAQ,EAAE,KAAK,GAAG,CAAC,GAAG,EAAE,QAAQ,GAAG,QAAQ,GAAG,GAAG,EAAE,kBAAkB,CAAC;AAAA,IACxI;AAAA,EACF;AACF;AACA,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,QAAM;AAAA,IACJ,SAAS;AAAA,EACX,IAAI,GAAG,GAAG,KAAK,CAAC;AAChB,SAAO,EAAE,CAAC;AACZ;", "names": []}