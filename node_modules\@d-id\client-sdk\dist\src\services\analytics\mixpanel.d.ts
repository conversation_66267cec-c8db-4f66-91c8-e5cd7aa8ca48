export interface AnalyticsOptions {
    token: string;
    agentId: string;
    isEnabled?: boolean;
    distinctId?: string;
}
export interface Analytics {
    token: string;
    distinct_id?: string;
    isEnabled: boolean;
    chatId?: string;
    agentId: string;
    owner_id?: string;
    getRandom(): string;
    track(event: string, props?: Record<string, any>): Promise<any>;
    linkTrack(mixpanelEvent: string, props: Record<string, any>, event: string, dependencies: string[]): any;
    enrich(props: Record<string, any>): void;
    additionalProperties: Record<string, any>;
}
export declare function initializeAnalytics(config: AnalyticsOptions): Analytics;
