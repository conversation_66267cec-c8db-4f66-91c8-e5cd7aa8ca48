import { CreateStreamOptions, StreamType } from '../../types';
import { StreamingManager } from '../streaming-manager';
export declare function validateInterrupt(streamingManager: StreamingManager<CreateStreamOptions> | undefined, streamType: StreamType | undefined, videoId: string | null): void;
export declare function sendInterrupt(streamingManager: StreamingManager<CreateStreamOptions>, videoId: string): Promise<void>;
