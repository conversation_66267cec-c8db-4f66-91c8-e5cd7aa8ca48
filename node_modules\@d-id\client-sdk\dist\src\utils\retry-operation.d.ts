interface RetryOptions {
    limit?: number;
    delayMs?: number;
    timeout?: number;
    timeoutErrorMessage?: string;
    shouldRetryFn?: (error: any) => boolean;
    onRetry?: (error: any) => void;
}
/**
 * Utility function to retry a promise operation with configurable retry logic
 *
 * @param operation - A function that returns a Promise to be retried
 * @param userOptions - Configuration options for retry behavior
 * @param userOptions.limit - Maximum number of retry attempts (default: 3)
 * @param userOptions.delayMs - Delay between retries in milliseconds (default: 0, no delay)
 * @param userOptions.timeout - Timeout for each attempt in milliseconds (default: 30000, set 0 to disable)
 * @param userOptions.timeoutErrorMessage - Custom timeout error message
 * @param userOptions.shouldRetryFn - Function to determine if retry should occur based on error, that will force throw even if limit is not reached when returns "false"
 *
 * @returns Promise that resolves with the operation result or rejects with the last error
 *
 * @throws {Error} Last error encountered after all retries are exhausted
 * @throws {Error} Timeout error if operation exceeds specified timeout
 *
 * @example
 * // With custom options
 * const result = await retryOperation(
 *   async () => await fetch(url),
 *   {
 *     limit: 5,
 *     delayMs: 1000,
 *     timeout: 5000,
 *     shouldRetryFn: (error) => error.status === 429
 *   }
 * );
 */
export declare function retryOperation<T>(operation: () => Promise<T>, userOptions?: RetryOptions): Promise<T>;
export {};
