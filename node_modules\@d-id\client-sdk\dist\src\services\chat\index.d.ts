import type { <PERSON>, AgentsAPI, Chat } from '../../types';
import { ChatMode } from '../../types';
import { Analytics } from '../analytics/mixpanel';
export declare function getRequestHeaders(chatMode?: ChatMode): Record<string, Record<string, string>>;
export declare function createChat(agent: Agent, agentsApi: AgentsAPI, analytics: Analytics, chatMode?: ChatMode, persist?: boolean, chat?: Chat): Promise<{
    chat: Chat | undefined;
    chatMode: ChatMode | undefined;
}>;
