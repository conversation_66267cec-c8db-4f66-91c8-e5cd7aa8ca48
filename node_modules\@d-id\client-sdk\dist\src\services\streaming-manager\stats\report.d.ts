import { AnalyticsRTCStatsReport, SlimRTCStatsReport } from '../../../types';
export interface VideoRTCStatsReport {
    webRTCStats: {
        anomalies: AnalyticsRTCStatsReport[];
        aggregateReport: AnalyticsRTCStatsReport;
        minRtt: number;
        maxRtt: number;
        avgRtt: number;
        minJitterDelayInInterval: number;
        maxJitterDelayInInterval: number;
        avgJitterDelayInInterval: number;
    };
    codec: string;
    resolution: string;
}
export declare function formatStats(stats: RTCStatsReport): SlimRTCStatsReport;
export declare function createVideoStatsReport(stats: SlimRTCStatsReport[], interval: number, previousStats?: SlimRTCStatsReport): VideoRTCStatsReport;
