# D-ID Avatar Chat - Setup Instructions

## Quick Start

Your D-ID Avatar Chat application is ready! Here's how to get it running:

### 1. Application is Already Running

The development server is running at: **http://localhost:3000**

### 2. Get Your D-ID Credentials

To use the application, you need:

1. **Agent ID**: `v2_agt_gLL2BIsAW` (already configured)
2. **Client Key**: You need to get this from D-ID Studio

#### How to get your Client Key:

1. Go to [D-ID Studio](https://studio.d-id.com/)
2. Log in with your account
3. Find the agent with ID `v2_agt_gLL2BIsAW` or create a new one
4. Click on the agent's "..." menu
5. Select "Embed"
6. Add these domains to the allowed list:
   - `localhost`
   - `127.0.0.1`
   - `localhost:3000`
   - `127.0.0.1:3000`
7. Copy the `data-client-key` value from the embed code

### 3. Configure the Application

1. Open http://localhost:3000 in your browser
2. Click the settings icon (⚙️) in the top-right corner
3. The Agent ID should already be filled: `v2_agt_gLL2BIsAW`
4. Paste your Client Key in the "Client Key" field
5. Click "Save Configuration"

### 4. Start Chatting

1. Click the "Connect" button
2. Wait for the status to show "Connected"
3. Type a message and press Enter
4. The avatar will respond with video and audio!

## Features Available

✅ **Real-time Avatar Streaming** - See the avatar respond in real-time  
✅ **Chat Interface** - Type messages and get responses  
✅ **Voice Input** - Click the microphone to speak (requires browser permission)  
✅ **Responsive Design** - Works on desktop and mobile  
✅ **Connection Status** - Monitor connection state  
✅ **Configuration Panel** - Easy setup of credentials  

## Troubleshooting

### "Please configure your client key" message
- You need to get a valid client key from D-ID Studio
- Make sure the domains are properly configured in D-ID Studio

### Connection fails
- Verify your client key is correct
- Check that your API key has sufficient credits
- Ensure your internet connection is stable

### Avatar doesn't appear
- Check browser console for errors (F12)
- Try refreshing the page
- Ensure WebRTC is supported in your browser

### Voice input doesn't work
- Grant microphone permissions when prompted
- Use a supported browser (Chrome, Edge, Safari)

## API Key Information

Your API key format: `<EMAIL>:-v21mVTNfZBsEcYS5PvXV`

This key is used for:
- Authenticating with D-ID API
- Accessing the agent `v2_agt_gLL2BIsAW`
- Creating client keys for browser usage

## Next Steps

1. **Test the Application**: Try different messages and see how the avatar responds
2. **Customize**: Modify the styling in `style.css` or add new features in `main.js`
3. **Deploy**: Use `npm run build` to create a production build
4. **Create New Agents**: Use D-ID Studio to create agents with different appearances

## Support

- **D-ID Documentation**: https://docs.d-id.com/
- **D-ID Studio**: https://studio.d-id.com/
- **Browser Console**: Press F12 to see detailed error messages

---

**Note**: The application is currently running in development mode. For production use, build the application with `npm run build` and serve the `dist` folder.
