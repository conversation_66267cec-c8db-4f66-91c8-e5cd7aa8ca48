import { Auth } from '../types/auth';
export type RequestOptions = RequestInit & {
    skipErrorHandler?: boolean;
};
export declare function createClient(auth: Auth, host?: string, onError?: (error: Error, errorData: object) => void): {
    get<T = any>(url: string, options?: RequestOptions): Promise<T>;
    post<T_1 = any>(url: string, body?: any, options?: RequestOptions): Promise<T_1>;
    delete<T_2 = any>(url: string, body?: any, options?: RequestOptions): Promise<T_2>;
    patch<T_3 = any>(url: string, body?: any, options?: RequestOptions): Promise<T_3>;
};
