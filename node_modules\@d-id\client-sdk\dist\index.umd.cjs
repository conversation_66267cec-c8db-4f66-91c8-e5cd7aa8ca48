(function(h,T){typeof exports=="object"&&typeof module<"u"?T(exports):typeof define=="function"&&define.amd?define(["exports"],T):(h=typeof globalThis<"u"?globalThis:h||self,T(h.index={}))})(this,function(h){"use strict";var It=Object.defineProperty;var Mt=(h,T,x)=>T in h?It(h,T,{enumerable:!0,configurable:!0,writable:!0,value:x}):h[T]=x;var te=(h,T,x)=>(Mt(h,typeof T!="symbol"?T+"":T,x),x);class T extends Error{constructor({kind:n,description:r,error:s}){super(JSON.stringify({kind:n,description:r}));te(this,"kind");te(this,"description");te(this,"error");this.kind=n,this.description=r,this.error=s}}class x extends T{constructor(t,n){super({kind:"ChatCreationFailed",description:`Failed to create ${n?"persistent":""} chat, mode: ${t}`})}}class se extends T{constructor(t){super({kind:"ChatModeDowngraded",description:`Chat mode downgraded to ${t}`})}}class V extends T{constructor(n,r){super({kind:"ValidationError",description:n});te(this,"key");this.key=r}}class oe extends T{constructor(t){super({kind:"WSError",description:t})}}var ce=(e=>(e.TRIAL="trial",e.BASIC="basic",e.ENTERPRISE="enterprise",e.LITE="lite",e.ADVANCED="advanced",e))(ce||{}),de=(e=>(e.TRIAL="deid-trial",e.PRO="deid-pro",e.ENTERPRISE="deid-enterprise",e.LITE="deid-lite",e.ADVANCED="deid-advanced",e.BUILD="deid-api-build",e.LAUNCH="deid-api-launch",e.SCALE="deid-api-scale",e))(de||{}),le=(e=>(e.Created="created",e.Started="started",e.Done="done",e.Error="error",e.Rejected="rejected",e.Ready="ready",e))(le||{}),ue=(e=>(e.Unrated="Unrated",e.Positive="Positive",e.Negative="Negative",e))(ue||{}),E=(e=>(e.Functional="Functional",e.TextOnly="TextOnly",e.Maintenance="Maintenance",e.Playground="Playground",e.DirectPlayback="DirectPlayback",e.Off="Off",e))(E||{}),q=(e=>(e.Embed="embed",e.Query="query",e.Partial="partial",e.Answer="answer",e.Complete="done",e))(q||{}),fe=(e=>(e.KnowledgeProcessing="knowledge/processing",e.KnowledgeIndexing="knowledge/indexing",e.KnowledgeFailed="knowledge/error",e.KnowledgeDone="knowledge/done",e))(fe||{}),me=(e=>(e.Knowledge="knowledge",e.Document="document",e.Record="record",e))(me||{}),ge=(e=>(e.Pdf="pdf",e.Text="text",e.Html="html",e.Word="word",e.Json="json",e.Markdown="markdown",e.Csv="csv",e.Excel="excel",e.Powerpoint="powerpoint",e.Archive="archive",e.Image="image",e.Audio="audio",e.Video="video",e))(ge||{}),he=(e=>(e.Clip="clip",e.Talk="talk",e))(he||{});const Pe=e=>{switch(e){case"clip":return"clip";case"talk":return"talk";default:throw new Error(`Unknown video type: ${e}`)}};var w=(e=>(e.Start="START",e.Stop="STOP",e))(w||{}),Q=(e=>(e.Strong="STRONG",e.Weak="WEAK",e.Unknown="UNKNOWN",e))(Q||{}),O=(e=>(e.Idle="IDLE",e.Talking="TALKING",e))(O||{}),j=(e=>(e.ChatAnswer="chat/answer",e.ChatPartial="chat/partial",e.StreamDone="stream/done",e.StreamStarted="stream/started",e.StreamFailed="stream/error",e.StreamReady="stream/ready",e.StreamCreated="stream/created",e.StreamInterrupt="stream/interrupt",e.StreamVideoCreated="stream-video/started",e.StreamVideoDone="stream-video/done",e.StreamVideoError="stream-video/error",e.StreamVideoRejected="stream-video/rejected",e))(j||{}),D=(e=>(e.New="new",e.Fail="fail",e.Connected="connected",e.Connecting="connecting",e.Closed="closed",e.Completed="completed",e.Disconnected="disconnected",e))(D||{}),U=(e=>(e.Legacy="legacy",e.Fluent="fluent",e))(U||{}),we=(e=>(e.Amazon="amazon",e.Microsoft="microsoft",e.Afflorithmics="afflorithmics",e.Elevenlabs="elevenlabs",e))(we||{}),pe=(e=>(e.Public="public",e.Premium="premium",e.Private="private",e))(pe||{});const be=45*1e3,Be="X-Playground-Chat",Z="https://api.d-id.com",Le="wss://notifications.d-id.com",$e="79f81a83a67430be2bc0fd61042b8faa",ye=e=>new Promise(t=>setTimeout(t,e)),G=(e=16)=>{const t=new Uint8Array(e);return window.crypto.getRandomValues(t),Array.from(t,n=>n.toString(16).padStart(2,"0")).join("").slice(0,13)},ze=e=>[E.TextOnly,E.Playground,E.Maintenance].includes(e),ve=e=>e&&[E.DirectPlayback,E.Off].includes(e);function Fe(e,t){let n;return{promise:new Promise((s,o)=>{n=setTimeout(()=>o(new Error(t)),e)}),clear:()=>clearTimeout(n)}}async function re(e,t){const n={limit:(t==null?void 0:t.limit)??3,delayMs:(t==null?void 0:t.delayMs)??0,timeout:(t==null?void 0:t.timeout)??3e4,timeoutErrorMessage:(t==null?void 0:t.timeoutErrorMessage)||"Timeout error",shouldRetryFn:(t==null?void 0:t.shouldRetryFn)??(()=>!0),onRetry:(t==null?void 0:t.onRetry)??(()=>{})};let r;for(let s=1;s<=n.limit;s++)try{if(!n.timeout)return await e();const{promise:o,clear:c}=Fe(n.timeout,n.timeoutErrorMessage),a=e().finally(c);return await Promise.race([a,o])}catch(o){if(r=o,!n.shouldRetryFn(o)||s>=n.limit)throw o;await ye(n.delayMs),n.onRetry(o)}throw r}function ke(){let e=window.localStorage.getItem("did_external_key_id");if(!e){let t=G();window.localStorage.setItem("did_external_key_id",t),e=t}return e}let Ne=G();function Ce(e){if(e.type==="bearer")return`Bearer ${e.token}`;if(e.type==="basic")return`Basic ${btoa(`${e.username}:${e.password}`)}`;if(e.type==="key")return`Client-Key ${e.clientKey}.${ke()}_${Ne}`;throw new Error(`Unknown auth type: ${e}`)}const Je=e=>re(e,{limit:3,delayMs:1e3,timeout:0,shouldRetryFn:t=>t.status===429});function De(e,t=Z,n){const r=async(s,o)=>{const{skipErrorHandler:c,...a}=o||{},i=await Je(()=>fetch(t+(s!=null&&s.startsWith("/")?s:`/${s}`),{...a,headers:{...a.headers,Authorization:Ce(e),"Content-Type":"application/json"}}));if(!i.ok){let d=await i.text().catch(()=>`Failed to fetch with status ${i.status}`);const l=new Error(d);throw n&&!c&&n(l,{url:s,options:a,headers:i.headers}),l}return i.json()};return{get(s,o){return r(s,{...o,method:"GET"})},post(s,o,c){return r(s,{...c,body:JSON.stringify(o),method:"POST"})},delete(s,o,c){return r(s,{...c,body:JSON.stringify(o),method:"DELETE"})},patch(s,o,c){return r(s,{...c,body:JSON.stringify(o),method:"PATCH"})}}}function Se(e,t=Z,n){const r=De(e,`${t}/agents`,n);return{create(s,o){return r.post("/",s,o)},getAgents(s,o){return r.get(`/${s?`?tag=${s}`:""}`,o).then(c=>c??[])},getById(s,o){return r.get(`/${s}`,o)},delete(s,o){return r.delete(`/${s}`,void 0,o)},update(s,o,c){return r.patch(`/${s}`,o,c)},newChat(s,o,c){return r.post(`/${s}/chat`,o,c)},chat(s,o,c,a){return r.post(`/${s}/chat/${o}`,c,a)},createRating(s,o,c,a){return r.post(`/${s}/chat/${o}/ratings`,c,a)},updateRating(s,o,c,a,i){return r.patch(`/${s}/chat/${o}/ratings/${c}`,a,i)},deleteRating(s,o,c,a){return r.delete(`/${s}/chat/${o}/ratings/${c}`,a)},getSTTToken(s,o){return r.get(`/${s}/stt-token`,o)}}}const Re=e=>e.type==="clip"&&e.presenter_id.startsWith("v2_")?"clip_v2":e.type;function We(e){var s,o,c,a;const t=()=>/Mobi|Android/i.test(navigator.userAgent)?"Mobile":"Desktop",n=()=>{const i=navigator.platform;return i.toLowerCase().includes("win")?"Windows":i.toLowerCase().includes("mac")?"Mac OS X":i.toLowerCase().includes("linux")?"Linux":"Unknown"},r=e.presenter;return{$os:`${n()}`,isMobile:`${t()=="Mobile"}`,browser:navigator.userAgent,origin:window.location.origin,agentType:Re(r),agentVoice:{voiceId:(o=(s=e.presenter)==null?void 0:s.voice)==null?void 0:o.voice_id,provider:(a=(c=e.presenter)==null?void 0:c.voice)==null?void 0:a.type}}}function Ue(e){var n,r,s,o,c,a;const t=(n=e.llm)==null?void 0:n.prompt_customization;return{agentType:Re(e.presenter),owner_id:e.owner_id??"",promptVersion:(r=e.llm)==null?void 0:r.prompt_version,behavior:{role:t==null?void 0:t.role,personality:t==null?void 0:t.personality,instructions:(s=e.llm)==null?void 0:s.instructions},temperature:(o=e.llm)==null?void 0:o.temperature,knowledgeSource:t==null?void 0:t.knowledge_source,starterQuestionsCount:(a=(c=e.knowledge)==null?void 0:c.starter_message)==null?void 0:a.length,topicsToAvoid:t==null?void 0:t.topics_to_avoid,maxResponseLength:t==null?void 0:t.max_response_length,agentId:e.id,access:e.access,name:e.preview_name,...e.access==="public"?{from:"agent-template"}:{}}}const Ke=e=>e.reduce((t,n)=>t+n,0),Ee=e=>Ke(e)/e.length;function He(e,t,n){var i,d,l;const{event:r,...s}=e,{template:o}=(t==null?void 0:t.llm)||{},{language:c}=((i=t==null?void 0:t.presenter)==null?void 0:i.voice)||{};return{...s,llm:{...s.llm,template:o},script:{...s.script,provider:{...(d=s==null?void 0:s.script)==null?void 0:d.provider,language:c}},stitch:(t==null?void 0:t.presenter.type)==="talk"?(l=t==null?void 0:t.presenter)==null?void 0:l.stitch:void 0,...n}}let ne={};const xe="https://api-js.mixpanel.com/track/?verbose=1&ip=1";function Ve(e){const t=window!=null&&window.hasOwnProperty("DID_AGENTS_API")?"agents-ui":"agents-sdk";return{token:e.token||"testKey",distinct_id:e.distinctId||ke(),agentId:e.agentId,additionalProperties:{},isEnabled:e.isEnabled??!0,getRandom:G,enrich(n){this.additionalProperties={...this.additionalProperties,...n}},async track(n,r){if(!this.isEnabled)return Promise.resolve();const{audioPath:s,...o}=r||{},c={method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({data:JSON.stringify([{event:n,properties:{...this.additionalProperties,...o,agentId:this.agentId,source:t,token:this.token,time:Date.now(),$insert_id:this.getRandom(),origin:window.location.href,"Screen Height":window.screen.height||window.innerWidth,"Screen Width":window.screen.width||window.innerHeight,"User Agent":navigator.userAgent}}])})};try{return await fetch(xe,c).then(a=>a.json())}catch(a){return console.error(a)}},linkTrack(n,r,s,o){ne[n]||(ne[n]={events:{},resolvedDependencies:[]}),o.includes(s)||o.push(s);const c=ne[n];if(c.events[s]={props:r},c.resolvedDependencies.push(s),o.every(i=>c.resolvedDependencies.includes(i))){const i=o.reduce((d,l)=>c.events[l]?{...d,...c.events[l].props}:d,{});this.track(n,i),c.resolvedDependencies=c.resolvedDependencies.filter(d=>!o.includes(d)),o.forEach(d=>{delete c.events[d]})}}}}function Ie(){let e=0;return{reset:()=>e=0,update:()=>e=Date.now(),get:(t=!1)=>t?Date.now()-e:e}}const z=Ie(),ae=Ie();function Me(e){return e===E.Playground?{headers:{[Be]:"true"}}:{}}async function Te(e,t,n,r,s=!1,o){try{return!o&&!ve(r)&&(o=await t.newChat(e.id,{persist:s},Me(r)),n.track("agent-chat",{event:"created",chatId:o.id,mode:r})),{chat:o,chatMode:(o==null?void 0:o.chat_mode)??r}}catch(c){try{const a=JSON.parse(c.message);if((a==null?void 0:a.kind)==="InsufficientCreditsError")throw new Error("InsufficientCreditsError")}catch(a){console.error("Error parsing the error message:",a)}throw new Error("Cannot create new chat")}}function qe(e){return e&&e.length>0?e:[]}function Oe(e,t,n){if(!e)throw new Error("Please connect to the agent first");if(!e.interruptAvailable)throw new Error("Interrupt is not enabled for this stream");if(t!==U.Fluent)throw new Error("Interrupt only available for Fluent streams");if(!n)throw new Error("No active video to interrupt")}async function Xe(e,t){const n={type:j.StreamInterrupt,videoId:t,timestamp:Date.now()};e.sendDataChannelMessage(JSON.stringify(n))}function Ye(e){return new Promise((t,n)=>{const{callbacks:r,host:s,auth:o}=e,{onMessage:c=null,onOpen:a=null,onClose:i=null,onError:d=null}=r||{},l=new WebSocket(`${s}?authorization=${Ce(o)}`);l.onmessage=c,l.onclose=i,l.onerror=v=>{console.error(v),d==null||d("Websocket failed to connect",v),n(v)},l.onopen=v=>{a==null||a(v),t(l)}})}async function Qe(e){const{retries:t=1}=e;let n=null;for(let r=0;(n==null?void 0:n.readyState)!==WebSocket.OPEN;r++)try{n=await Ye(e)}catch(s){if(r===t)throw s;await ye(r*500)}return n}async function Ge(e,t,n){const r=n!=null&&n.onMessage?[n.onMessage]:[],s=await Qe({auth:e,host:t,callbacks:{onError:o=>{var c;return(c=n.onError)==null?void 0:c.call(n,new oe(o))},onMessage(o){const c=JSON.parse(o.data);r.forEach(a=>a(c.event,c))}}});return{socket:s,disconnect:()=>s.close(),subscribeToEvents:o=>r.push(o)}}function Ze(e){if(e.answer!==void 0)return e.answer;let t=0,n="";for(;t in e;)n+=e[t++];return n}function et(e,t,n,r,s){const o=r.messages[r.messages.length-1];if(!(e===q.Partial||e===q.Answer)||(o==null?void 0:o.role)!=="assistant")return;const{content:c,sequence:a}=t;e===q.Partial?n[a]=c:n.answer=c;const i=Ze(n);(o.content!==i||e===q.Answer)&&(o.content=i,s==null||s([...r.messages],e))}function tt(e,t,n,r,s){let o={};return{clearQueue:()=>o={},onMessage:(c,a)=>{var i,d;if("content"in a)et(c,a,o,t,n.callbacks.onNewMessage),c===q.Answer&&e.track("agent-message-received",{messages:t.messages.length,mode:t.chatMode});else{const l=j,v=[l.StreamVideoDone,l.StreamVideoError,l.StreamVideoRejected],A=[l.StreamFailed,l.StreamVideoError,l.StreamVideoRejected],P=He(a,r,{mode:t.chatMode});if(c=c,c===l.StreamVideoCreated)e.linkTrack("agent-video",P,l.StreamVideoCreated,["start"]);else if(v.includes(c)){const b=c.split("/")[1];A.includes(c)?e.track("agent-video",{...P,event:b}):e.linkTrack("agent-video",{...P,event:b},c,["done"])}A.includes(c)&&((d=(i=n.callbacks).onError)==null||d.call(i,new Error(`Stream failed with event ${c}`),{data:a})),a.event===l.StreamDone&&s()}}}}function rt(e,t,n,r){const s=De(e,`${t}/agents/${n}`,r);return{createStream(o){return s.post("/streams",o)},startConnection(o,c,a){return s.post(`/streams/${o}/sdp`,{session_id:a,answer:c})},addIceCandidate(o,c,a){return s.post(`/streams/${o}/ice`,{session_id:a,...c})},sendStreamRequest(o,c,a){return s.post(`/streams/${o}`,{session_id:c,...a})},close(o,c){return s.delete(`/streams/${o}`,{session_id:c})}}}function nt(e,t,n){const r=(t.timestamp-e.timestamp)/1e3;return{duration:r,bytesReceived:t.bytesReceived-e.bytesReceived,bitrate:Math.round((t.bytesReceived-e.bytesReceived)*8/r),packetsReceived:t.packetsReceived-e.packetsReceived,packetsLost:t.packetsLost-e.packetsLost,framesDropped:t.framesDropped-e.framesDropped,framesDecoded:t.framesDecoded-e.framesDecoded,jitter:t.jitter,avgJitterDelayInInterval:(t.jitterBufferDelay-e.jitterBufferDelay)/(t.jitterBufferEmittedCount-e.jitterBufferEmittedCount),jitterBufferEmittedCount:t.jitterBufferEmittedCount-e.jitterBufferEmittedCount,jitterBufferDelay:(t.jitterBufferDelay-e.jitterBufferDelay)/r,framesPerSecond:t.framesPerSecond,freezeCount:t.freezeCount-e.freezeCount,freezeDuration:t.freezeDuration-e.freezeDuration,lowFpsCount:n}}function at(e){return e.filter(t=>t.freezeCount>0||t.framesPerSecond<21||t.framesDropped>0||t.packetsLost>0).map(t=>{const{timestamp:n,...r}=t,s=[];return t.freezeCount>0&&s.push("freeze"),t.framesPerSecond<21&&s.push("low fps"),t.framesDropped>0&&s.push("frames dropped"),t.packetsLost>0&&s.push("packet loss"),{...r,causes:s}})}function it(e){let t="",n=0;for(const r of e.values())if(r&&r.type==="codec"&&r.mimeType.startsWith("video")&&(t=r.mimeType.split("/")[1]),r&&r.type==="candidate-pair"&&(n=r.currentRoundTripTime),r&&r.type==="inbound-rtp"&&r.kind==="video")return{codec:t,rtt:n,timestamp:r.timestamp,bytesReceived:r.bytesReceived,packetsReceived:r.packetsReceived,packetsLost:r.packetsLost,framesDropped:r.framesDropped,framesDecoded:r.framesDecoded,jitter:r.jitter,jitterBufferDelay:r.jitterBufferDelay,jitterBufferEmittedCount:r.jitterBufferEmittedCount,avgJitterDelayInInterval:r.jitterBufferDelay/r.jitterBufferEmittedCount,frameWidth:r.frameWidth,frameHeight:r.frameHeight,framesPerSecond:r.framesPerSecond,freezeCount:r.freezeCount,freezeDuration:r.totalFreezesDuration};return{}}function st(e,t,n){const r=e.map((i,d)=>d===0?n?{timestamp:i.timestamp,duration:0,rtt:i.rtt,bytesReceived:i.bytesReceived-n.bytesReceived,bitrate:(i.bytesReceived-n.bytesReceived)*8/(t/1e3),packetsReceived:i.packetsReceived-n.packetsReceived,packetsLost:i.packetsLost-n.packetsLost,framesDropped:i.framesDropped-n.framesDropped,framesDecoded:i.framesDecoded-n.framesDecoded,jitter:i.jitter,jitterBufferDelay:i.jitterBufferDelay-n.jitterBufferDelay,jitterBufferEmittedCount:i.jitterBufferEmittedCount-n.jitterBufferEmittedCount,avgJitterDelayInInterval:(i.jitterBufferDelay-n.jitterBufferDelay)/(i.jitterBufferEmittedCount-n.jitterBufferEmittedCount),framesPerSecond:i.framesPerSecond,freezeCount:i.freezeCount-n.freezeCount,freezeDuration:i.freezeDuration-n.freezeDuration}:{timestamp:i.timestamp,rtt:i.rtt,duration:0,bytesReceived:i.bytesReceived,bitrate:i.bytesReceived*8/(t/1e3),packetsReceived:i.packetsReceived,packetsLost:i.packetsLost,framesDropped:i.framesDropped,framesDecoded:i.framesDecoded,jitter:i.jitter,jitterBufferDelay:i.jitterBufferDelay,jitterBufferEmittedCount:i.jitterBufferEmittedCount,avgJitterDelayInInterval:i.jitterBufferDelay/i.jitterBufferEmittedCount,framesPerSecond:i.framesPerSecond,freezeCount:i.freezeCount,freezeDuration:i.freezeDuration}:{timestamp:i.timestamp,duration:t*d/1e3,rtt:i.rtt,bytesReceived:i.bytesReceived-e[d-1].bytesReceived,bitrate:(i.bytesReceived-e[d-1].bytesReceived)*8/(t/1e3),packetsReceived:i.packetsReceived-e[d-1].packetsReceived,packetsLost:i.packetsLost-e[d-1].packetsLost,framesDropped:i.framesDropped-e[d-1].framesDropped,framesDecoded:i.framesDecoded-e[d-1].framesDecoded,jitter:i.jitter,jitterBufferDelay:i.jitterBufferDelay-e[d-1].jitterBufferDelay,jitterBufferEmittedCount:i.jitterBufferEmittedCount-e[d-1].jitterBufferEmittedCount,avgJitterDelayInInterval:(i.jitterBufferDelay-e[d-1].jitterBufferDelay)/(i.jitterBufferEmittedCount-e[d-1].jitterBufferEmittedCount),framesPerSecond:i.framesPerSecond,freezeCount:i.freezeCount-e[d-1].freezeCount,freezeDuration:i.freezeDuration-e[d-1].freezeDuration}),s=at(r),o=s.reduce((i,d)=>i+(d.causes.includes("low fps")?1:0),0),c=r.filter(i=>!!i.avgJitterDelayInInterval).map(i=>i.avgJitterDelayInInterval),a=r.filter(i=>!!i.rtt).map(i=>i.rtt);return{webRTCStats:{anomalies:s,minRtt:Math.min(...a),avgRtt:Ee(a),maxRtt:Math.max(...a),aggregateReport:nt(e[0],e[e.length-1],o),minJitterDelayInInterval:Math.min(...c),maxJitterDelayInInterval:Math.max(...c),avgJitterDelayInInterval:Ee(c)},codec:e[0].codec,resolution:`${e[0].frameWidth}x${e[0].frameHeight}`}}const ie=100,ot=Math.max(Math.ceil(400/ie),1),ct=.25,dt=.28;function lt(){let e=0,t,n,r=0;return s=>{for(const o of s.values())if(o&&o.type==="inbound-rtp"&&o.kind==="video"){const c=o.jitterBufferDelay,a=o.jitterBufferEmittedCount;if(n&&a>n){const l=c-t,v=a-n;r=l/v}t=c,n=a;const i=o.framesDecoded,d=i-e>0;return e=i,{isReceiving:d,avgJitterDelayInInterval:r,freezeCount:o.freezeCount}}return{isReceiving:!1,avgJitterDelayInInterval:r}}}function ut(e,t,n,r,s,o=!1){let c=[],a,i=0,d=!1,l=Q.Unknown,v=Q.Unknown,A=0,P=0;const b=lt();return setInterval(async()=>{const K=await e.getStats(),{isReceiving:I,avgJitterDelayInInterval:X,freezeCount:Y}=b(K),B=it(K);if(I)i=0,A=Y-P,v=X<ct?Q.Strong:X>dt&&A>1?Q.Weak:l,v!==l&&(s==null||s(v),l=v,P+=A,A=0),d||(r==null||r(w.Start),a=c[c.length-1],c=[],d=!0),c.push(B);else if(d&&(i++,i>=ot)){const u=st(c,ie,a);r==null||r(w.Stop,u),t()||n(),P=Y,d=!1}},ie)}let _e=!1;const $=(e,t)=>_e&&console.log(e,t),ft=(window.RTCPeerConnection||window.webkitRTCPeerConnection||window.mozRTCPeerConnection).bind(window);function je(e){switch(e){case"connected":return D.Connected;case"checking":return D.Connecting;case"failed":return D.Fail;case"new":return D.New;case"closed":return D.Closed;case"disconnected":return D.Disconnected;case"completed":return D.Completed;default:return D.New}}function mt(e){const[t,n=""]=e.split(/:(.+)/);try{const r=JSON.parse(n);return $("parsed data channel message",{subject:t,data:r}),{subject:t,data:r}}catch(r){return $("Failed to parse data channel message, returning data as string",{subject:t,rawData:n,error:r}),{subject:t,data:n}}}function gt({statsSignal:e,dataChannelSignal:t,onVideoStateChange:n,report:r}){e===w.Start&&t===w.Start?n==null||n(w.Start):e===w.Stop&&t===w.Stop&&(n==null||n(w.Stop,r))}function ht({statsSignal:e,dataChannelSignal:t,onVideoStateChange:n,onAgentActivityStateChange:r,report:s}){e===w.Start?n==null||n(w.Start):e===w.Stop&&(n==null||n(w.Stop,s)),t===w.Start?r==null||r(O.Talking):t===w.Stop&&(r==null||r(O.Idle))}function Ae({statsSignal:e,dataChannelSignal:t,onVideoStateChange:n,onAgentActivityStateChange:r,streamType:s,report:o}){s===U.Legacy?gt({statsSignal:e,dataChannelSignal:t,onVideoStateChange:n,report:o}):s===U.Fluent&&ht({statsSignal:e,dataChannelSignal:t,onVideoStateChange:n,onAgentActivityStateChange:r,report:o})}async function wt(e,t,{debug:n=!1,callbacks:r,auth:s,baseURL:o=Z,analytics:c}){var H;_e=n;let a=!1,i=!1,d=w.Stop,l=w.Stop;const{startConnection:v,sendStreamRequest:A,close:P,createStream:b,addIceCandidate:K}=rt(s,o,e,r.onError),{id:I,offer:X,ice_servers:Y,session_id:B,fluent:u,interrupt_enabled:k}=await b(t);(H=r.onStreamCreated)==null||H.call(r,{stream_id:I,session_id:B,agent_id:e});const m=new ft({iceServers:Y}),S=m.createDataChannel("JanusDataChannel");if(!B)throw new Error("Could not create session_id");const p=u?U.Fluent:U.Legacy;c.enrich({"stream-type":p});const y=t.stream_warmup&&!u,L=()=>a,F=()=>{var f;a=!0,i&&((f=r.onConnectionStateChange)==null||f.call(r,D.Connected))},N=ut(m,L,F,(f,g)=>Ae({statsSignal:l=f,dataChannelSignal:p===U.Legacy?d:void 0,onVideoStateChange:r.onVideoStateChange,onAgentActivityStateChange:r.onAgentActivityStateChange,report:g,streamType:p}),f=>{var g;return(g=r.onConnectivityStateChange)==null?void 0:g.call(r,f)},y);m.onicecandidate=f=>{var g;$("peerConnection.onicecandidate",f);try{f.candidate&&f.candidate.sdpMid&&f.candidate.sdpMLineIndex!==null?K(I,{candidate:f.candidate.candidate,sdpMid:f.candidate.sdpMid,sdpMLineIndex:f.candidate.sdpMLineIndex},B):K(I,{candidate:null},B)}catch(M){(g=r.onError)==null||g.call(r,M,{streamId:I})}},S.onopen=()=>{i=!0,(!y||a)&&F()};const C=f=>{var g;(g=r.onVideoIdChange)==null||g.call(r,f)};function _(f,g){if(f===j.StreamStarted&&typeof g=="object"&&"metadata"in g){const M=g.metadata;C(M.videoId)}f===j.StreamDone&&C(null),d=f===j.StreamStarted?w.Start:w.Stop,Ae({statsSignal:p===U.Legacy?l:void 0,dataChannelSignal:d,onVideoStateChange:r.onVideoStateChange,onAgentActivityStateChange:r.onAgentActivityStateChange,streamType:p})}function R(f,g){const M=typeof g=="string"?g:g==null?void 0:g.metadata;M&&c.enrich({streamMetadata:M}),c.track("agent-chat",{event:"ready"})}const J={[j.StreamStarted]:_,[j.StreamDone]:_,[j.StreamReady]:R};S.onmessage=f=>{var ee;const{subject:g,data:M}=mt(f.data);(ee=J[g])==null||ee.call(J,g,M)},m.oniceconnectionstatechange=()=>{var g;$("peerConnection.oniceconnectionstatechange => "+m.iceConnectionState);const f=je(m.iceConnectionState);f!==D.Connected&&((g=r.onConnectionStateChange)==null||g.call(r,f))},m.ontrack=f=>{var g;$("peerConnection.ontrack",f),(g=r.onSrcObjectReady)==null||g.call(r,f.streams[0])},await m.setRemoteDescription(X),$("set remote description OK");const W=await m.createAnswer();return $("create answer OK"),await m.setLocalDescription(W),$("set local description OK"),await v(I,W,B),$("start connection OK"),{speak(f){return A(I,B,f)},async disconnect(){var f;if(I){const g=je(m.iceConnectionState);if(m){if(g===D.New){clearInterval(N);return}m.close(),m.oniceconnectionstatechange=null,m.onnegotiationneeded=null,m.onicecandidate=null,m.ontrack=null}try{g===D.Connected&&await P(I,B).catch(M=>{})}catch(M){$("Error on close stream connection",M)}(f=r.onAgentActivityStateChange)==null||f.call(r,O.Idle),clearInterval(N)}},sendDataChannelMessage(f){var g,M;if(!a||S.readyState!=="open"){$("Data channel is not ready for sending messages"),(g=r.onError)==null||g.call(r,new Error("Data channel is not ready for sending messages"),{streamId:I});return}try{S.send(f)}catch(ee){$("Error sending data channel message",ee),(M=r.onError)==null||M.call(r,ee,{streamId:I})}},sessionId:B,streamId:I,streamType:p,interruptAvailable:k}}function pt(e){const{streamOptions:t}=e??{};return{output_resolution:t==null?void 0:t.outputResolution,session_timeout:t==null?void 0:t.sessionTimeout,stream_warmup:t==null?void 0:t.streamWarmup,compatibility_mode:t==null?void 0:t.compatibilityMode,fluent:t==null?void 0:t.fluent}}function yt(e,t,n,r,s){s===U.Fluent?vt(e,t,n,r,s):Ct(e,t,n,r,s)}function vt(e,t,n,r,s){e===w.Start?r.track("stream-session",{event:"start","stream-type":s}):e===w.Stop&&r.track("stream-session",{event:"stop",is_greenscreen:t.presenter.type==="clip"&&t.presenter.is_greenscreen,background:t.presenter.type==="clip"&&t.presenter.background,"stream-type":s,...n})}function kt(e,t,n,r){z.get()<=0||(e===w.Start?n.linkTrack("agent-video",{event:"start",latency:z.get(!0),"stream-type":r},"start",[j.StreamVideoCreated]):e===w.Stop&&n.linkTrack("agent-video",{event:"stop",is_greenscreen:t.presenter.type==="clip"&&t.presenter.is_greenscreen,background:t.presenter.type==="clip"&&t.presenter.background,"stream-type":r},"done",[j.StreamVideoDone]))}function Ct(e,t,n,r,s){z.get()<=0||(e===w.Start?r.linkTrack("agent-video",{event:"start",latency:z.get(!0),"stream-type":s},"start",[j.StreamVideoCreated]):e===w.Stop&&r.linkTrack("agent-video",{event:"stop",is_greenscreen:t.presenter.type==="clip"&&t.presenter.is_greenscreen,background:t.presenter.type==="clip"&&t.presenter.background,"stream-type":s,...n},"done",[j.StreamVideoDone]))}function Dt(e,t,n){return z.reset(),new Promise(async(r,s)=>{try{const o=await wt(e.id,pt(t),{...t,analytics:n,callbacks:{...t.callbacks,onConnectionStateChange:c=>{var a,i;(i=(a=t.callbacks).onConnectionStateChange)==null||i.call(a,c),c===D.Connected&&r(o)},onVideoStateChange:(c,a)=>{var i,d;(d=(i=t.callbacks).onVideoStateChange)==null||d.call(i,c),yt(c,e,a,n,o.streamType)},onAgentActivityStateChange:c=>{var a,i;(i=(a=t.callbacks).onAgentActivityStateChange)==null||i.call(a,c),c===O.Talking?ae.update():ae.reset(),kt(c===O.Talking?w.Start:w.Stop,e,n,o.streamType)}}})}catch(o){s(o)}})}async function St(e,t,n,r,s){var v,A,P,b;const o=Te(e,n,r,t.mode,t.persistentChat,s),c=Dt(e,t,r),[a,i]=await Promise.all([o,c]),{chat:d,chatMode:l}=a;return l&&l!==t.mode&&(t.mode=l,(A=(v=t.callbacks).onModeChange)==null||A.call(v,l),l!==E.Functional)?((b=(P=t.callbacks).onError)==null||b.call(P,new se(l)),i==null||i.disconnect(),{chat:d}):{chat:d,streamingManager:i}}async function Rt(e,t){var X,Y,B;let n=!0,r=null;const s=t.mixpanelKey||$e,o=t.wsURL||Le,c=t.baseURL||Z,a={messages:[],chatMode:t.mode||E.Functional},i=Ve({token:s,agentId:e,isEnabled:t.enableAnalitics,distinctId:t.distinctId});i.track("agent-sdk",{event:"init"});const d=Se(t.auth,c,t.callbacks.onError),l=await d.getById(e);i.enrich(Ue(l));const{onMessage:v,clearQueue:A}=tt(i,a,t,l,()=>{var u;return(u=a.socketManager)==null?void 0:u.disconnect()});a.messages=qe(t.initialMessages),(Y=(X=t.callbacks).onNewMessage)==null||Y.call(X,[...a.messages],"answer");const P=u=>{r=u};i.track("agent-sdk",{event:"loaded",...We(l)});async function b(u){var L,F,N,C,_,R,J;(F=(L=t.callbacks).onConnectionStateChange)==null||F.call(L,D.Connecting),z.reset(),u&&!n&&(delete a.chat,(C=(N=t.callbacks).onNewMessage)==null||C.call(N,[...a.messages],"answer"));const k=t.mode===E.DirectPlayback?Promise.resolve(void 0):Ge(t.auth,o,{onMessage:v,onError:t.callbacks.onError}),m=re(()=>St(l,{...t,callbacks:{...t.callbacks,onVideoIdChange:P}},d,i,a.chat),{limit:3,timeout:be,timeoutErrorMessage:"Timeout initializing the stream",shouldRetryFn:W=>(W==null?void 0:W.message)!=="Could not connect"&&W.status!==429,delayMs:1e3}).catch(W=>{var H,f;throw I(E.Maintenance),(f=(H=t.callbacks).onConnectionStateChange)==null||f.call(H,D.Fail),W}),[S,{streamingManager:p,chat:y}]=await Promise.all([k,m]);y&&y.id!==((_=a.chat)==null?void 0:_.id)&&((J=(R=t.callbacks).onNewChat)==null||J.call(R,y.id)),a.streamingManager=p,a.socketManager=S,a.chat=y,n=!1,i.enrich({chatId:y==null?void 0:y.id,streamId:p==null?void 0:p.streamId,mode:a.chatMode}),I((y==null?void 0:y.chat_mode)??t.mode??E.Functional)}async function K(){var u,k,m,S;(u=a.socketManager)==null||u.disconnect(),await((k=a.streamingManager)==null?void 0:k.disconnect()),delete a.streamingManager,delete a.socketManager,(S=(m=t.callbacks).onConnectionStateChange)==null||S.call(m,D.Disconnected)}async function I(u){var k,m;u!==a.chatMode&&(i.track("agent-mode-change",{mode:u}),a.chatMode=u,a.chatMode!==E.Functional&&await K(),(m=(k=t.callbacks).onModeChange)==null||m.call(k,u))}return{agent:l,getStreamType:()=>{var u;return(u=a.streamingManager)==null?void 0:u.streamType},getIsInterruptAvailable:()=>{var u;return((u=a.streamingManager)==null?void 0:u.interruptAvailable)??!1},starterMessages:((B=l.knowledge)==null?void 0:B.starter_message)||[],getSTTToken:()=>d.getSTTToken(l.id),changeMode:I,enrichAnalytics:i.enrich,async connect(){await b(!0),i.track("agent-chat",{event:"connect",mode:a.chatMode})},async reconnect(){await K(),await b(!1),i.track("agent-chat",{event:"reconnect",mode:a.chatMode})},async disconnect(){await K(),i.track("agent-chat",{event:"disconnect",mode:a.chatMode})},async chat(u){var p,y,L,F,N;const k=()=>{if(ve(t.mode))throw new V(`${t.mode} is enabled, chat is disabled`);if(u.length>=800)throw new V("Message cannot be more than 800 characters");if(u.length===0)throw new V("Message cannot be empty");if(a.chatMode===E.Maintenance)throw new V("Chat is in maintenance mode");if(![E.TextOnly,E.Playground].includes(a.chatMode)){if(!a.streamingManager)throw new V("Streaming manager is not initialized");if(!a.chat)throw new V("Chat is not initialized")}},m=async()=>{var C,_;if(!a.chat){const R=await Te(l,d,i,a.chatMode,t.persistentChat);if(!R.chat)throw new x(a.chatMode,!!t.persistentChat);a.chat=R.chat,(_=(C=t.callbacks).onNewChat)==null||_.call(C,a.chat.id)}return a.chat.id},S=async(C,_)=>re(()=>{var R,J;return d.chat(l.id,_,{chatMode:a.chatMode,streamId:(R=a.streamingManager)==null?void 0:R.streamId,sessionId:(J=a.streamingManager)==null?void 0:J.sessionId,messages:C.map(({matches:W,...H})=>H)},{...Me(a.chatMode),skipErrorHandler:!0})},{limit:2,shouldRetryFn:R=>{var H,f,g,M;const J=(H=R==null?void 0:R.message)==null?void 0:H.includes("missing or invalid session_id");return!((f=R==null?void 0:R.message)==null?void 0:f.includes("Stream Error"))&&!J?((M=(g=t.callbacks).onError)==null||M.call(g,R),!1):!0},onRetry:async()=>{await K(),await b(!1)}});try{A(),k(),a.messages.push({id:G(),role:"user",content:u,created_at:new Date(z.update()).toISOString()}),(y=(p=t.callbacks).onNewMessage)==null||y.call(p,[...a.messages],"user");const C=await m(),_=await S([...a.messages],C);return a.messages.push({id:G(),role:"assistant",content:_.result||"",created_at:new Date().toISOString(),context:_.context,matches:_.matches}),i.track("agent-message-send",{event:"success",messages:a.messages.length+1}),_.result&&((F=(L=t.callbacks).onNewMessage)==null||F.call(L,[...a.messages],"answer"),i.track("agent-message-received",{latency:z.get(!0),messages:a.messages.length})),_}catch(C){throw((N=a.messages[a.messages.length-1])==null?void 0:N.role)==="assistant"&&a.messages.pop(),i.track("agent-message-send",{event:"error",messages:a.messages.length}),C}},rate(u,k,m){var y,L,F,N;const S=a.messages.find(C=>C.id===u);if(a.chat){if(!S)throw new Error("Message not found")}else throw new Error("Chat is not initialized");const p=((y=S.matches)==null?void 0:y.map(C=>[C.document_id,C.id]))??[];return i.track("agent-rate",{event:m?"update":"create",thumb:k===1?"up":"down",knowledge_id:((L=l.knowledge)==null?void 0:L.id)??"",matches:p,score:k}),m?d.updateRating(l.id,a.chat.id,m,{knowledge_id:((F=l.knowledge)==null?void 0:F.id)??"",message_id:u,matches:p,score:k}):d.createRating(l.id,a.chat.id,{knowledge_id:((N=l.knowledge)==null?void 0:N.id)??"",message_id:u,matches:p,score:k})},deleteRate(u){if(!a.chat)throw new Error("Chat is not initialized");return i.track("agent-rate-delete",{type:"text"}),d.deleteRating(l.id,a.chat.id,u)},async speak(u){var p,y,L;function k(){if(typeof u=="string"){if(!l.presenter.voice)throw new Error("Presenter voice is not initialized");return{type:"text",provider:l.presenter.voice,input:u,ssml:!1}}if(u.type==="text"&&!u.provider){if(!l.presenter.voice)throw new Error("Presenter voice is not initialized");return{type:"text",provider:l.presenter.voice,input:u.input,ssml:u.ssml}}return u}const m=k();if(i.track("agent-speak",m),z.update(),a.messages&&m.type==="text"&&(a.messages.push({id:G(),role:"assistant",content:m.input,created_at:new Date(z.get(!0)).toISOString()}),(y=(p=t.callbacks).onNewMessage)==null||y.call(p,[...a.messages],"answer")),ze(a.chatMode))return{duration:0,video_id:"",status:"success"};if(!a.streamingManager)throw new Error("Please connect to the agent first");return a.streamingManager.speak({script:m,metadata:{chat_id:(L=a.chat)==null?void 0:L.id,agent_id:l.id}})},async interrupt({type:u}){var m,S,p;Oe(a.streamingManager,(m=a.streamingManager)==null?void 0:m.streamType,r);const k=a.messages[a.messages.length-1];i.track("agent-video-interrupt",{type:u||"click",video_duration_to_interrupt:ae.get(!0),message_duration_to_interrupt:z.get(!0)}),k.interrupted=!0,(p=(S=t.callbacks).onNewMessage)==null||p.call(S,[...a.messages],"answer"),Xe(a.streamingManager,r)}}}function Et(e,t,n){const{getById:r}=Se(t,n||Z);return r(e)}h.AgentActivityState=O,h.AgentStatus=le,h.ChatCreationFailed=x,h.ChatMode=E,h.ChatModeDowngraded=se,h.ChatProgress=q,h.ConnectionState=D,h.ConnectivityState=Q,h.DocumentType=ge,h.KnowledgeType=me,h.PlanGroup=de,h.Providers=we,h.RateState=ue,h.StreamEvents=j,h.StreamType=U,h.StreamingState=w,h.Subject=fe,h.UserPlan=ce,h.ValidationError=V,h.VideoType=he,h.VoiceAccess=pe,h.WsError=oe,h.createAgentManager=Rt,h.getAgent=Et,h.mapVideoType=Pe,Object.defineProperty(h,Symbol.toStringTag,{value:"Module"})});
