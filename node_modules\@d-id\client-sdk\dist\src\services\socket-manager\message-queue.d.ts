import { Agent, AgentManagerOptions, ChatProgress, StreamEvents } from '../../types';
import { AgentManagerItems } from '../agent-manager';
import { Analytics } from '../analytics/mixpanel';
export interface ChatEventQueue {
    [sequence: number]: string;
    answer?: string;
}
export declare function createMessageEventQueue(analytics: Analytics, items: AgentManagerItems, options: AgentManagerOptions, agentEntity: Agent, onStreamDone: () => void): {
    clearQueue: () => {};
    onMessage: (event: ChatProgress | StreamEvents, data: any) => void;
};
