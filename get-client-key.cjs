// Script to generate a client key for D-ID agent
// Run with: node get-client-key.cjs

const https = require('https');

const API_KEY = '<EMAIL>:-v21mVTNfZBsEcYS5PvXV';
const AGENT_ID = 'v2_agt_gLL2BIsAW';

// Encode API key for Basic Auth
const encodedKey = Buffer.from(API_KEY).toString('base64');

const postData = JSON.stringify({
    allowed_domains: ['localhost', '127.0.0.1', 'localhost:3000', '127.0.0.1:3000']
});

const options = {
    hostname: 'api.d-id.com',
    port: 443,
    path: `/agents/${AGENT_ID}/client-key`,
    method: 'POST',
    headers: {
        'Authorization': `Basic ${encodedKey}`,
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
    }
};

console.log('Creating client key for agent:', AGENT_ID);
console.log('Allowed domains: localhost, 127.0.0.1, localhost:3000, 127.0.0.1:3000');

const req = https.request(options, (res) => {
    console.log(`Status: ${res.statusCode}`);

    let data = '';
    res.on('data', (chunk) => {
        data += chunk;
    });

    res.on('end', () => {
        try {
            const response = JSON.parse(data);
            if (res.statusCode === 200 || res.statusCode === 201) {
                console.log('\n✅ Client key created successfully!');
                console.log('Client Key:', response.client_key);
                console.log('\nUse this client key in your application configuration.');
            } else {
                console.log('\n❌ Error creating client key:');
                console.log(response);
            }
        } catch (error) {
            console.log('\n❌ Error parsing response:');
            console.log('Raw response:', data);
        }
    });
});

req.on('error', (error) => {
    console.error('Request error:', error);
});

req.write(postData);
req.end();
